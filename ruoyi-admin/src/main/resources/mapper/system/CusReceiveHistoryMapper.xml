<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CusReceiveHistoryMapper">
    
    <resultMap type="CusReceiveHistory" id="CusReceiveHistoryResult">
        <result property="id"    column="id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="requestId"    column="request_id"    />
        <result property="partnerId"    column="partner_id"    />
        <result property="serviceCode"    column="service_code"    />
        <result property="timestamp"    column="timestamp"    />
        <result property="msgDigest"    column="msg_digest"    />
        <result property="msgData"    column="msg_data"    />
        <result property="finalResult"    column="final_result"    />
        <result property="wayBillNo"    column="way_bill_no"    />
        <result property="wpServiceCode"    column="wp_service_code"    />
        <result property="imei"    column="imei"    />
        <result property="sn"    column="sn"    />
        <result property="photoList"    column="photo_list"    />
    </resultMap>

    <sql id="selectCusReceiveHistoryVo">
        select id, create_time, update_time, request_id, partner_id, service_code, timestamp, msg_digest, msg_data, final_result, way_bill_no, wp_service_code, imei, sn, photo_list from cus_receive_history
    </sql>

    <select id="selectCusReceiveHistoryList" parameterType="CusReceiveHistory" resultMap="CusReceiveHistoryResult">
        <include refid="selectCusReceiveHistoryVo"/>
        <where>  
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
            <if test="requestId != null  and requestId != ''"> and request_id = #{requestId}</if>
            <if test="partnerId != null  and partnerId != ''"> and partner_id = #{partnerId}</if>
            <if test="serviceCode != null  and serviceCode != ''"> and service_code = #{serviceCode}</if>
            <if test="timestamp != null "> and timestamp = #{timestamp}</if>
            <if test="msgDigest != null  and msgDigest != ''"> and msg_digest = #{msgDigest}</if>
            <if test="msgData != null  and msgData != ''"> and msg_data = #{msgData}</if>
            <if test="finalResult != null  and finalResult != ''"> and final_result = #{finalResult}</if>
            <if test="wayBillNo != null  and wayBillNo != ''"> and way_bill_no = #{wayBillNo}</if>
            <if test="wpServiceCode != null  and wpServiceCode != ''"> and wp_service_code = #{wpServiceCode}</if>
            <if test="imei != null  and imei != ''"> and imei = #{imei}</if>
            <if test="sn != null  and sn != ''"> and sn = #{sn}</if>
            <if test="photoList != null  and photoList != ''"> and photo_list = #{photoList}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectCusReceiveHistoryById" parameterType="Long" resultMap="CusReceiveHistoryResult">
        <include refid="selectCusReceiveHistoryVo"/>
        where id = #{id}
    </select>

    <insert id="insertCusReceiveHistory" parameterType="CusReceiveHistory" useGeneratedKeys="true" keyProperty="id">
        insert into cus_receive_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="requestId != null">request_id,</if>
            <if test="partnerId != null">partner_id,</if>
            <if test="serviceCode != null">service_code,</if>
            <if test="timestamp != null">timestamp,</if>
            <if test="msgDigest != null">msg_digest,</if>
            <if test="msgData != null">msg_data,</if>
            <if test="finalResult != null">final_result,</if>
            <if test="wayBillNo != null">way_bill_no,</if>
            <if test="wpServiceCode != null">wp_service_code,</if>
            <if test="imei != null">imei,</if>
            <if test="sn != null">sn,</if>
            <if test="photoList != null">photo_list,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="requestId != null">#{requestId},</if>
            <if test="partnerId != null">#{partnerId},</if>
            <if test="serviceCode != null">#{serviceCode},</if>
            <if test="timestamp != null">#{timestamp},</if>
            <if test="msgDigest != null">#{msgDigest},</if>
            <if test="msgData != null">#{msgData},</if>
            <if test="finalResult != null">#{finalResult},</if>
            <if test="wayBillNo != null">#{wayBillNo},</if>
            <if test="wpServiceCode != null">#{wpServiceCode},</if>
            <if test="imei != null">#{imei},</if>
            <if test="sn != null">#{sn},</if>
            <if test="photoList != null">#{photoList},</if>
         </trim>
    </insert>

    <update id="updateCusReceiveHistory" parameterType="CusReceiveHistory">
        update cus_receive_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="requestId != null">request_id = #{requestId},</if>
            <if test="partnerId != null">partner_id = #{partnerId},</if>
            <if test="serviceCode != null">service_code = #{serviceCode},</if>
            <if test="timestamp != null">timestamp = #{timestamp},</if>
            <if test="msgDigest != null">msg_digest = #{msgDigest},</if>
            <if test="msgData != null">msg_data = #{msgData},</if>
            <if test="finalResult != null">final_result = #{finalResult},</if>
            <if test="wayBillNo != null">way_bill_no = #{wayBillNo},</if>
            <if test="wpServiceCode != null">wp_service_code = #{wpServiceCode},</if>
            <if test="imei != null">imei = #{imei},</if>
            <if test="sn != null">sn = #{sn},</if>
            <if test="photoList != null">photo_list = #{photoList},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCusReceiveHistoryById" parameterType="Long">
        delete from cus_receive_history where id = #{id}
    </delete>

    <delete id="deleteCusReceiveHistoryByIds" parameterType="String">
        delete from cus_receive_history where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CusRequestHistoryMapper">
    
    <resultMap type="CusRequestHistory" id="CusRequestHistoryResult">
        <result property="id"    column="id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="requestStr"    column="request_str"    />
        <result property="responseStr1"    column="response_str_1"    />
        <result property="responseStr2"    column="response_str_2"    />
        <result property="successFlag1"    column="success_flag_1"    />
        <result property="successFlag2"    column="success_flag_2"    />
        <result property="totalFlag"    column="total_flag"    />
        <result property="requestId"    column="request_id"    />
    </resultMap>

    <sql id="selectCusRequestHistoryVo">
        select id, create_time, update_time, request_str, response_str_1, response_str_2, success_flag_1, success_flag_2, total_flag, request_id from cus_request_history
    </sql>

    <select id="selectCusRequestHistoryList" parameterType="CusRequestHistory" resultMap="CusRequestHistoryResult">
        <include refid="selectCusRequestHistoryVo"/>
        <where>  
            <if test="requestStr != null  and requestStr != ''"> and request_str = #{requestStr}</if>
            <if test="responseStr1 != null  and responseStr1 != ''"> and response_str_1 = #{responseStr1}</if>
            <if test="responseStr2 != null  and responseStr2 != ''"> and response_str_2 = #{responseStr2}</if>
            <if test="successFlag1 != null  and successFlag1 != ''"> and success_flag_1 = #{successFlag1}</if>
            <if test="successFlag2 != null  and successFlag2 != ''"> and success_flag_2 = #{successFlag2}</if>
            <if test="totalFlag != null  and totalFlag != ''"> and total_flag = #{totalFlag}</if>
            <if test="requestId != null  and requestId != ''"> and request_id = #{requestId}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectCusRequestHistoryById" parameterType="Long" resultMap="CusRequestHistoryResult">
        <include refid="selectCusRequestHistoryVo"/>
        where id = #{id}
    </select>

    <insert id="insertCusRequestHistory" parameterType="CusRequestHistory">
        insert into cus_request_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="requestStr != null">request_str,</if>
            <if test="responseStr1 != null">response_str_1,</if>
            <if test="responseStr2 != null">response_str_2,</if>
            <if test="successFlag1 != null">success_flag_1,</if>
            <if test="successFlag2 != null">success_flag_2,</if>
            <if test="totalFlag != null">total_flag,</if>
            <if test="requestId != null">request_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="requestStr != null">#{requestStr},</if>
            <if test="responseStr1 != null">#{responseStr1},</if>
            <if test="responseStr2 != null">#{responseStr2},</if>
            <if test="successFlag1 != null">#{successFlag1},</if>
            <if test="successFlag2 != null">#{successFlag2},</if>
            <if test="totalFlag != null">#{totalFlag},</if>
            <if test="requestId != null">#{requestId},</if>
         </trim>
    </insert>

    <update id="updateCusRequestHistory" parameterType="CusRequestHistory">
        update cus_request_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="requestStr != null">request_str = #{requestStr},</if>
            <if test="responseStr1 != null">response_str_1 = #{responseStr1},</if>
            <if test="responseStr2 != null">response_str_2 = #{responseStr2},</if>
            <if test="successFlag1 != null">success_flag_1 = #{successFlag1},</if>
            <if test="successFlag2 != null">success_flag_2 = #{successFlag2},</if>
            <if test="totalFlag != null">total_flag = #{totalFlag},</if>
            <if test="requestId != null">request_id = #{requestId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCusRequestHistoryById" parameterType="Long">
        delete from cus_request_history where id = #{id}
    </delete>

    <delete id="deleteCusRequestHistoryByIds" parameterType="String">
        delete from cus_request_history where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CusOrderInfoMapper">
    
    <resultMap type="CusOrderInfo" id="CusOrderInfoResult">
        <result property="id"    column="id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="waybillNo"    column="waybill_no"    />
        <result property="subsidySource"    column="subsidy_source"    />
        <result property="wpMerchantCode"    column="wp_merchant_code"    />
        <result property="wpServiceCode"    column="wp_service_code"    />
        <result property="cargoName"    column="cargo_name"    />
        <result property="snCode"    column="sn_code"    />
        <result property="imelCode"    column="imel_code"    />
        <result property="checkPhoneNo"    column="check_phone_no"    />
        <result property="checkMonthCard"    column="check_month_card"    />
        <result property="finalResult"    column="final_result"    />
        <result property="logId"    column="log_id"    />
        <result property="localPhotoList" column="local_photo_list"/>
    </resultMap>

    <sql id="selectCusOrderInfoVo">
        select id, create_time, update_time, waybill_no, subsidy_source, wp_merchant_code, wp_service_code, cargo_name, sn_code, imel_code, check_phone_no, check_month_card, final_result, log_id, local_photo_list from cus_order_info
    </sql>

    <select id="selectCusOrderInfoList" parameterType="CusOrderInfo" resultMap="CusOrderInfoResult">
        <include refid="selectCusOrderInfoVo"/>
        <where>  
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''"> and update_time between #{params.beginUpdateTime} and #{params.endUpdateTime}</if>
            <if test="waybillNo != null and waybillNo != ''"> and waybill_no = #{waybillNo}</if>
            <if test="subsidySource != null  and subsidySource != ''"> and subsidy_source = #{subsidySource}</if>
            <if test="wpMerchantCode != null  and wpMerchantCode != ''"> and wp_merchant_code = #{wpMerchantCode}</if>
            <if test="wpServiceCode != null  and wpServiceCode != ''"> and wp_service_code = #{wpServiceCode}</if>
            <if test="cargoName != null  and cargoName != ''"> and cargo_name like concat('%', #{cargoName}, '%')</if>
            <if test="snCode != null  and snCode != ''"> and sn_code = #{snCode}</if>
            <if test="imelCode != null  and imelCode != ''"> and imel_code = #{imelCode}</if>
            <if test="checkPhoneNo != null and checkPhoneNo != ''"> and check_phone_no = #{checkPhoneNo}</if>
            <if test="checkMonthCard != null  and checkMonthCard != ''"> and check_month_card = #{checkMonthCard}</if>
            <if test="finalResult != null  and finalResult != ''"> and final_result = #{finalResult}</if>
            <if test="logId != null  and logId != ''"> and log_id = #{logId}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectCusOrderInfoListToCall" parameterType="CusOrderInfo" resultMap="CusOrderInfoResult">
        <include refid="selectCusOrderInfoVo"/>
        <where>
            final_result = 'N'
        </where>
    </select>
    
    <select id="selectCusOrderInfoById" parameterType="Long" resultMap="CusOrderInfoResult">
        <include refid="selectCusOrderInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertCusOrderInfo" parameterType="CusOrderInfo">
        insert into cus_order_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="waybillNo != null">waybill_no,</if>
            <if test="subsidySource != null">subsidy_source,</if>
            <if test="wpMerchantCode != null">wp_merchant_code,</if>
            <if test="wpServiceCode != null">wp_service_code,</if>
            <if test="cargoName != null">cargo_name,</if>
            <if test="snCode != null">sn_code,</if>
            <if test="imelCode != null">imel_code,</if>
            <if test="checkPhoneNo != null">check_phone_no,</if>
            <if test="checkMonthCard != null">check_month_card,</if>
            <if test="finalResult != null">final_result,</if>
            <if test="logId != null">log_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="waybillNo != null">#{waybillNo},</if>
            <if test="subsidySource != null">#{subsidySource},</if>
            <if test="wpMerchantCode != null">#{wpMerchantCode},</if>
            <if test="wpServiceCode != null">#{wpServiceCode},</if>
            <if test="cargoName != null">#{cargoName},</if>
            <if test="snCode != null">#{snCode},</if>
            <if test="imelCode != null">#{imelCode},</if>
            <if test="checkPhoneNo != null">#{checkPhoneNo},</if>
            <if test="checkMonthCard != null">#{checkMonthCard},</if>
            <if test="finalResult != null">#{finalResult},</if>
            <if test="logId != null">#{logId},</if>
         </trim>
    </insert>

    <update id="updateCusOrderInfo" parameterType="CusOrderInfo">
        update cus_order_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="waybillNo != null">waybill_no = #{waybillNo},</if>
            <if test="subsidySource != null">subsidy_source = #{subsidySource},</if>
            <if test="wpMerchantCode != null">wp_merchant_code = #{wpMerchantCode},</if>
            <if test="wpServiceCode != null">wp_service_code = #{wpServiceCode},</if>
            <if test="cargoName != null">cargo_name = #{cargoName},</if>
            <if test="snCode != null">sn_code = #{snCode},</if>
            <if test="imelCode != null">imel_code = #{imelCode},</if>
            <if test="checkPhoneNo != null">check_phone_no = #{checkPhoneNo},</if>
            <if test="checkMonthCard != null">check_month_card = #{checkMonthCard},</if>
            <if test="finalResult != null">final_result = #{finalResult},</if>
            <if test="logId != null">log_id = #{logId},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateCusOrderInfoByWaybill" parameterType="CusOrderInfo">
        update cus_order_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="localPhotoList != null">local_photo_list = #{localPhotoList},</if>
        </trim>
        where waybill_no = #{waybillNo}
    </update>

    <delete id="deleteCusOrderInfoById" parameterType="Long">
        delete from cus_order_info where id = #{id}
    </delete>

    <delete id="deleteCusOrderInfoByIds" parameterType="String">
        delete from cus_order_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCusOrderInfoListToCallByIds" parameterType="CusOrderInfo" resultMap="CusOrderInfoResult">
        <include refid="selectCusOrderInfoVo"/>
        <where>
            id in
            <foreach item="id" collection="array" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>

</mapper>
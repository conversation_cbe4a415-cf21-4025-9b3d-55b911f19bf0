<!DOCTYPE html>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--360浏览器优先以webkit内核解析-->
    <title>高飞易微派管理系统</title>
    <link rel="shortcut icon" href="favicon.ico">
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/style.min.css" th:href="@{/css/style.min.css}" rel="stylesheet"/>
</head>

<body class="gray-bg">
    <div class="welcome-container">
        <div class="welcome-header text-center">
            <div class="welcome-icon">
                <i class="fa fa-rocket fa-5x text-primary"></i>
            </div>
            <h1 class="welcome-title">欢迎使用高飞易微派管理系统</h1>
            <p class="welcome-subtitle">现代化企业级管理平台，助力您的业务腾飞</p>
        </div>
        
        <div class="welcome-features">
            <div class="row">
                <div class="col-md-4 col-sm-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fa fa-shield fa-3x text-success"></i>
                        </div>
                        <h3>安全可靠</h3>
                        <p>采用先进的安全框架，保障系统数据安全</p>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fa fa-cogs fa-3x text-info"></i>
                        </div>
                        <h3>高效管理</h3>
                        <p>智能化管理工具，提升工作效率</p>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fa fa-mobile fa-3x text-warning"></i>
                        </div>
                        <h3>响应式设计</h3>
                        <p>完美适配各种设备，随时随地办公</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="welcome-footer text-center">
            <div class="footer-content">
                <p class="footer-text">© 2024 高飞易微派管理系统 - 专业的企业级管理解决方案</p>
                <div class="footer-links">
                    <a href="#" class="footer-link"><i class="fa fa-question-circle"></i> 帮助文档</a>
                    <a href="#" class="footer-link"><i class="fa fa-phone"></i> 技术支持</a>
                    <a href="#" class="footer-link"><i class="fa fa-envelope"></i> 联系我们</a>
                </div>
            </div>
        </div>
    </div>
    
    <style>
        .welcome-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            overflow: hidden;
        }
        
        .welcome-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .welcome-header {
            position: relative;
            z-index: 2;
            margin-bottom: 60px;
        }
        
        .welcome-icon {
            margin-bottom: 30px;
            animation: bounce 2s infinite;
        }
        
        .welcome-title {
            color: #ffffff;
            font-size: 3.5rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            animation: fadeInUp 1s ease-out;
        }
        
        .welcome-subtitle {
            color: #f8f9fa;
            font-size: 1.3rem;
            font-weight: 300;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.3s both;
        }
        
        .welcome-features {
            position: relative;
            z-index: 2;
            max-width: 1200px;
            width: 100%;
            margin-bottom: 60px;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 40px 30px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            margin-bottom: 20px;
        }
        
        .feature-card h3 {
            color: #333;
            font-size: 1.5rem;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .feature-card p {
            color: #666;
            font-size: 1rem;
            line-height: 1.6;
            margin: 0;
        }
        
        .welcome-footer {
            position: relative;
            z-index: 2;
            margin-top: auto;
        }
        
        .footer-content {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .footer-text {
            color: #ffffff;
            font-size: 1rem;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        
        .footer-links {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .footer-link {
            color: #ffffff;
            text-decoration: none;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            opacity: 0.8;
        }
        
        .footer-link:hover {
            color: #ffffff;
            opacity: 1;
            text-decoration: none;
            transform: translateY(-2px);
        }
        
        .footer-link i {
            margin-right: 8px;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @media (max-width: 768px) {
            .welcome-title {
                font-size: 2.5rem;
            }
            
            .welcome-subtitle {
                font-size: 1.1rem;
            }
            
            .feature-card {
                padding: 30px 20px;
            }
            
            .footer-links {
                gap: 20px;
            }
            
            .footer-link {
                font-size: 0.9rem;
            }
        }
        
        @media (max-width: 480px) {
            .welcome-container {
                padding: 20px 15px;
            }
            
            .welcome-title {
                font-size: 2rem;
            }
            
            .welcome-subtitle {
                font-size: 1rem;
            }
            
            .footer-links {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
    
    <script th:src="@{/js/jquery.min.js}"></script>
    <script th:src="@{/js/bootstrap.min.js}"></script>
    <script th:src="@{/ajax/libs/layer/layer.min.js}"></script>
</body>
</html>

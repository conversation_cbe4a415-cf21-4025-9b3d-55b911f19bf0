<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改接口响应记录')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-receiveHistory-edit" th:object="${cusReceiveHistory}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">请求id：</label>
                    <div class="col-sm-8">
                        <input name="requestId" th:field="*{requestId}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">合作伙伴编码：</label>
                    <div class="col-sm-8">
                        <input name="partnerId" th:field="*{partnerId}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">合作伙伴编码：</label>
                    <div class="col-sm-8">
                        <input name="serviceCode" th:field="*{serviceCode}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">时间戳：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="timestamp" th:value="${#dates.format(cusReceiveHistory.timestamp, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">数字签名：</label>
                    <div class="col-sm-8">
                        <input name="msgDigest" th:field="*{msgDigest}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">业务数据报文：</label>
                    <div class="col-sm-8">
                        <textarea name="msgData" class="form-control">[[*{msgData}]]</textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">是否处理：</label>
                    <div class="col-sm-8">
                        <input name="finalResult" th:field="*{finalResult}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">客户运单号：</label>
                    <div class="col-sm-8">
                        <input name="wayBillNo" th:field="*{wayBillNo}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">微派任务编码：</label>
                    <div class="col-sm-8">
                        <input name="wpServiceCode" th:field="*{wpServiceCode}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">imei：</label>
                    <div class="col-sm-8">
                        <input name="imei" th:field="*{imei}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">sn：</label>
                    <div class="col-sm-8">
                        <input name="sn" th:field="*{sn}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">图片列表：</label>
                    <div class="col-sm-8">
                        <textarea name="photoList" class="form-control">[[*{photoList}]]</textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/receiveHistory";
        $("#form-receiveHistory-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-receiveHistory-edit').serialize());
            }
        }

        $("input[name='timestamp']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
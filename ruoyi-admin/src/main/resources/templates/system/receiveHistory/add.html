<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增接口响应记录')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-receiveHistory-add">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">请求id：</label>
                    <div class="col-sm-8">
                        <input name="requestId" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">合作伙伴编码：</label>
                    <div class="col-sm-8">
                        <input name="partnerId" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">合作伙伴编码：</label>
                    <div class="col-sm-8">
                        <input name="serviceCode" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">时间戳：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="timestamp" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">数字签名：</label>
                    <div class="col-sm-8">
                        <input name="msgDigest" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">业务数据报文：</label>
                    <div class="col-sm-8">
                        <textarea name="msgData" class="form-control"></textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">是否处理：</label>
                    <div class="col-sm-8">
                        <input name="finalResult" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">客户运单号：</label>
                    <div class="col-sm-8">
                        <input name="wayBillNo" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">微派任务编码：</label>
                    <div class="col-sm-8">
                        <input name="wpServiceCode" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">imei：</label>
                    <div class="col-sm-8">
                        <input name="imei" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">sn：</label>
                    <div class="col-sm-8">
                        <input name="sn" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">图片列表：</label>
                    <div class="col-sm-8">
                        <textarea name="photoList" class="form-control"></textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/receiveHistory"
        $("#form-receiveHistory-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-receiveHistory-add').serialize());
            }
        }

        $("input[name='timestamp']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
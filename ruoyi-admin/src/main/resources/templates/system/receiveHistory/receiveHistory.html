<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('接口响应记录列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li class="select-time">
                                <label>创建时间：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateTime]"/>
                            </li>
<!--                            <li>-->
<!--                                <label>更新时间：</label>-->
<!--                                <input type="text" class="time-input" placeholder="请选择更新时间" name="updateTime"/>-->
<!--                            </li>-->
                            <li>
                                <label>请求id：</label>
                                <input type="text" name="requestId"/>
                            </li>
                            <li>
                                <label>合作伙伴编码：</label>
                                <input type="text" name="partnerId"/>
                            </li>
                            <li>
                                <label>合作伙伴编码：</label>
                                <input type="text" name="serviceCode"/>
                            </li>
                            <li>
                                <label>时间戳：</label>
                                <input type="text" class="time-input" placeholder="请选择时间戳" name="timestamp"/>
                            </li>
                            <li>
                                <label>数字签名：</label>
                                <input type="text" name="msgDigest"/>
                            </li>
                            <li>
                                <label>是否处理：</label>
                                <input type="text" name="finalResult"/>
                            </li>
                            <li>
                                <label>客户运单号：</label>
                                <input type="text" name="wayBillNo"/>
                            </li>
                            <li>
                                <label>微派任务编码：</label>
                                <input type="text" name="wpServiceCode"/>
                            </li>
                            <li>
                                <label>imei：</label>
                                <input type="text" name="imei"/>
                            </li>
                            <li>
                                <label>sn：</label>
                                <input type="text" name="sn"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
<!--                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:receiveHistory:add">-->
<!--                    <i class="fa fa-plus"></i> 添加-->
<!--                </a>-->
<!--                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:receiveHistory:edit">-->
<!--                    <i class="fa fa-edit"></i> 修改-->
<!--                </a>-->
<!--                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:receiveHistory:remove">-->
<!--                    <i class="fa fa-remove"></i> 删除-->
<!--                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:receiveHistory:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:receiveHistory:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:receiveHistory:remove')}]];
        var prefix = ctx + "system/receiveHistory";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "接口响应记录",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'createTime',
                    title: '创建时间'
                },
                // {
                //     field: 'updateTime',
                //     title: '更新时间'
                // },
                {
                    field: 'requestId',
                    title: '请求id'
                },
                {
                    field: 'partnerId',
                    title: '合作伙伴编码'
                },
                {
                    field: 'serviceCode',
                    title: '合作伙伴编码'
                },
                {
                    field: 'timestamp',
                    title: '时间戳'
                },
                // {
                //     field: 'msgDigest',
                //     title: '数字签名'
                // },
                // {
                //     field: 'msgData',
                //     title: '业务数据报文'
                // },
                {
                    field: 'finalResult',
                    title: '是否处理'
                },
                {
                    field: 'wayBillNo',
                    title: '客户运单号'
                },
                {
                    field: 'wpServiceCode',
                    title: '微派任务编码'
                },
                {
                    field: 'imei',
                    title: 'imei'
                },
                {
                    field: 'sn',
                    title: 'sn'
                },
                // {
                //     field: 'photoList',
                //     title: '图片列表'
                // },
                {
                    title: '图片',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        var photoList = row.photoList || '';
                        actions.push('<button type="button" class="btn btn-primary photo-gallery-btn" data-photos="' + photoList + '">查看图片</button>');
                        // actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                },
                    {
                        title: '操作',
                        align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        // actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        // actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        $(document).on('click', '.photo-gallery-btn', function(){
            var photoList = $(this).data('photos');
            var photoData = [];
            
            if (photoList && photoList.trim() !== '') {
                var photos = photoList.split(',');
                for (var i = 0; i < photos.length; i++) {
                    var photoUrl = photos[i].trim();
                    if (photoUrl) {
                        photoData.push({
                            "alt": "图片" + (i + 1),
                            "pid": i + 1,
                            "src": photoUrl,
                            "thumb": photoUrl
                        });
                    }
                }
            }
            
            if (photoData.length === 0) {
                layer.msg('暂无图片');
                return;
            }
            
            var json = {
                "title": "图片相册",
                "id": 123,
                "start": 0,
                "data": photoData
            };
            
            layer.photos({
                photos: json,
                closeBtn: 0,
                anim: 5
            });
        })
    </script>
</body>
</html>
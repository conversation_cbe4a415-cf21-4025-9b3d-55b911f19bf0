<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('订单信息列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li class="select-time">
                                <label>创建时间：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateTime]"/>
                            </li>
                            <li class="select-time">
                                <label>更新时间：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginUpdateTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endUpdateTime]"/>
                            </li>
                            <li>
                                <label>客户运单号：</label>
                                <input type="text" name="waybillNo"/>
                            </li>
                            <li>
                                <label>国补标识：</label>
                                <input type="text" name="subsidySource"/>
                            </li>
                            <li>
                                <label>微派商户编码：</label>
                                <input type="text" name="wpMerchantCode"/>
                            </li>
                            <li>
                                <label>微派任务编码：</label>
                                <input type="text" name="wpServiceCode"/>
                            </li>
                            <li>
                                <label>托寄物名称：</label>
                                <input type="text" name="cargoName"/>
                            </li>
                            <li>
                                <label>sn编码：</label>
                                <input type="text" name="snCode"/>
                            </li>
                            <li>
                                <label>imei编码：</label>
                                <input type="text" name="imelCode"/>
                            </li>
                            <li>
                                <label>手机号码后四位：</label>
                                <input type="text" name="checkPhoneNo"/>
                            </li>
                            <li>
                                <label>月结卡号：</label>
                                <input type="text" name="checkMonthCard"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:orderInfo:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:orderInfo:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:orderInfo:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-info" onclick="$.table.importExcel()" >
                    <i class="fa fa-upload"></i> 导入
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:orderInfo:export">
                    <i class="fa fa-download"></i> 导出
                </a>
                <a class="btn btn-danger" onclick="refreshCache()" >
                    <i class="fa fa-refresh"></i> 触发微派接口调用(全量)
                </a>
                <a class="btn btn-danger" onclick="$.operate.touchAll()" >
                    <i class="fa fa-refresh"></i> 触发微派接口调用(选中项)
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        // 尝试多种方式获取 ctx
        var ctx1 = /*[[@{/}]]*/ '';
        var ctx2 = [[@{/}]];
        var ctx3 = /*[[${#httpServletRequest.contextPath}]]*/ '';
        var ctx4 = [[${#httpServletRequest.contextPath}]];

        // 调试信息
        console.log('ctx1 (comment style):', ctx1);
        console.log('ctx2 (direct style):', ctx2);
        console.log('ctx3 (contextPath comment):', ctx3);
        console.log('ctx4 (contextPath direct):', ctx4);

        // 使用最合适的 ctx
        var ctx = ctx2 || ctx4 || ctx1 || ctx3 || '/';
        console.log('Final ctx value:', ctx);

        var editFlag = [[${@permission.hasPermi('system:orderInfo:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:orderInfo:remove')}]];
        var prefix = ctx + "system/orderInfo";

        console.log('prefix value:', prefix);

        // 检查是否有全局的 ctx 变量（来自 include footer）
        setTimeout(function() {
            if (typeof window.ctx !== 'undefined') {
                console.log('Global ctx from include footer:', window.ctx);
            } else {
                console.log('No global ctx found from include footer');
            }
        }, 100);

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                touchUrl: prefix + "/touchAll",
                exportUrl: prefix + "/export",
                importUrl: prefix + "/importData",
                importTemplateUrl: prefix + "/importTemplate",
                modalName: "订单信息",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'createTime',
                    title: '创建时间'
                },
                {
                    field: 'updateTime',
                    title: '更新时间'
                },
                {
                    field: 'waybillNo',
                    title: '客户运单号'
                },
                {
                    field: 'subsidySource',
                    title: '国补标识'
                },
                {
                    field: 'wpMerchantCode',
                    title: '微派商户编码'
                },
                {
                    field: 'wpServiceCode',
                    title: '微派任务编码'
                },
                {
                    field: 'cargoName',
                    title: '托寄物名称'
                },
                {
                    field: 'snCode',
                    title: 'sn编码'
                },
                {
                    field: 'imelCode',
                    title: 'imei编码'
                },
                {
                    field: 'checkPhoneNo',
                    title: '手机号码后四位'
                },
                {
                    field: 'checkMonthCard',
                    title: '月结卡号'
                },
                {
                    field: 'finalResult',
                    title: '调用结果'
                },
                {
                    field: 'logId',
                    title: '调用日志id'
                },
                {
                    title: '查看日志',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return '<button type="button" class="btn btn-primary view-log-btn" data-logid="' + row.logId + '">查看日志</button>';
                    }
                },
                {
                    title: '图片',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        var photoList = row.localPhotoList || '';
                        actions.push('<button type="button" class="btn btn-primary photo-gallery-btn" data-photos="' + photoList + '">查看图片</button>');
                        return actions.join('');
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
        /** 刷新参数缓存 */
        function refreshCache() {
            $.operate.get(prefix + "/touchCall");
        }
        
        /** 查看日志按钮点击事件 */
        $(document).on('click', '.view-log-btn', function(){
            var logId = $(this).data('logid');
            $.modal.open('查看日志详情', ctx + 'system/history/detail/' + logId);
        });
        
        /** 查看图片按钮点击事件 */
        $(document).on('click', '.photo-gallery-btn', function(){
            var photoList = $(this).data('photos');
            var photoData = [];
            console.log('ctx value:', ctx);
            if (photoList && photoList.trim() !== '') {
                var photos = photoList.split(',');
                for (var i = 0; i < photos.length; i++) {
                    var photoUrl = photos[i].trim();
                    if (photoUrl) {
                        photoData.push({
                            "alt": "图片" + (i + 1),
                            "pid": i + 1,
                            "src": ctx + photoUrl,
                            "thumb": ctx + photoUrl
                        });
                    }
                }
            }
            
            if (photoData.length === 0) {
                layer.msg('暂无图片');
                return;
            }
            
            var json = {
                "title": "图片相册",
                "id": 123,
                "start": 0,
                "data": photoData
            };
            
            layer.photos({
                photos: json,
                closeBtn: 0,
                anim: 5
            });
        });
    </script>
</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10">
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</html>
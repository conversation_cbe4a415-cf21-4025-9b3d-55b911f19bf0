<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改订单信息')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-orderInfo-edit" th:object="${cusOrderInfo}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">客户运单号：</label>
                    <div class="col-sm-8">
                        <input name="waybillNo" th:field="*{waybillNo}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">国补标识：</label>
                    <div class="col-sm-8">
                        <input name="subsidySource" th:field="*{subsidySource}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">微派商户编码：</label>
                    <div class="col-sm-8">
                        <input name="wpMerchantCode" th:field="*{wpMerchantCode}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">微派任务编码：</label>
                    <div class="col-sm-8">
                        <input name="wpServiceCode" th:field="*{wpServiceCode}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">托寄物名称：</label>
                    <div class="col-sm-8">
                        <input name="cargoName" th:field="*{cargoName}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">sn编码：</label>
                    <div class="col-sm-8">
                        <input name="snCode" th:field="*{snCode}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">imei编码：</label>
                    <div class="col-sm-8">
                        <input name="imelCode" th:field="*{imelCode}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">手机号码后四位：</label>
                    <div class="col-sm-8">
                        <input name="checkPhoneNo" th:field="*{checkPhoneNo}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">月结卡号：</label>
                    <div class="col-sm-8">
                        <input name="checkMonthCard" th:field="*{checkMonthCard}" class="form-control" type="text">
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "system/orderInfo";
        $("#form-orderInfo-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-orderInfo-edit').serialize());
            }
        }
    </script>
</body>
</html>
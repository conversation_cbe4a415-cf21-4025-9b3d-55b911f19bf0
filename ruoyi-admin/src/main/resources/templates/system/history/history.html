<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('接口请求记录列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>一级是否成功：</label>
                                <input type="text" name="successFlag1"/>
                            </li>
                            <li>
                                <label>二级是否成功：</label>
                                <input type="text" name="successFlag2"/>
                            </li>
                            <li>
                                <label>总体是否成功：</label>
                                <input type="text" name="totalFlag"/>
                            </li>
                            <li>
                                <label>请求id：</label>
                                <input type="text" name="requestId"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
<!--                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:history:add">-->
<!--                    <i class="fa fa-plus"></i> 添加-->
<!--                </a>-->
<!--                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:history:edit">-->
<!--                    <i class="fa fa-edit"></i> 修改-->
<!--                </a>-->
<!--                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:history:remove">-->
<!--                    <i class="fa fa-remove"></i> 删除-->
<!--                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:history:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:history:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:history:remove')}]];
        var prefix = ctx + "system/history";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "接口请求记录",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'createTime',
                    title: '创建时间'
                },
                // {
                //     field: 'requestStr',
                //     title: '请求体',
                //     width: 80
                // },
                {
                    field: 'responseStr1',
                    title: '一级响应体',
                    width: 80
                },
                {
                    field: 'responseStr2',
                    title: '二级响应体',
                    width: 80
                },
                {
                    field: 'successFlag1',
                    title: '一级是否成功'
                },
                {
                    field: 'successFlag2',
                    title: '二级是否成功'
                },
                {
                    field: 'totalFlag',
                    title: '总体是否成功'
                },
                {
                    field: 'requestId',
                    title: '请求id'
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
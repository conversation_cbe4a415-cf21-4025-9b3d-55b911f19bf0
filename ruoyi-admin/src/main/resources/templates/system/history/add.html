<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增接口请求记录')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-history-add">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">请求体：</label>
                    <div class="col-sm-8">
                        <textarea name="requestStr" class="form-control"></textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">一级响应体：</label>
                    <div class="col-sm-8">
                        <textarea name="responseStr1" class="form-control"></textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">二级响应体：</label>
                    <div class="col-sm-8">
                        <textarea name="responseStr2" class="form-control"></textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">一级是否成功：</label>
                    <div class="col-sm-8">
                        <input name="successFlag1" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">二级是否成功：</label>
                    <div class="col-sm-8">
                        <input name="successFlag2" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">总体是否成功：</label>
                    <div class="col-sm-8">
                        <input name="totalFlag" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">请求id：</label>
                    <div class="col-sm-8">
                        <input name="requestId" class="form-control" type="text">
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "system/history"
        $("#form-history-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-history-add').serialize());
            }
        }
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('接口请求记录详细')" />
	<th:block th:include="include :: jsonview-css" />
</head>
<body class="white-bg">
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
	<form class="form-horizontal m-t" id="signupForm">
		<div class="form-group">
			<label class="col-sm-2 control-label">请求ID：</label>
			<div class="form-control-static" th:text="${cusRequestHistory.requestId}">
			</div>
		</div>
		<div class="form-group">
			<label class="col-sm-2 control-label">请求体：</label>
			<div class="form-control-static"><pre id="requestParam"></pre></div>
		</div>
		<div class="form-group">
			<label class="col-sm-2 control-label">一级响应体：</label>
			<div class="form-control-static"><pre id="responseStr1"></pre></div>
		</div>
		<div class="form-group">
			<label class="col-sm-2 control-label">二级响应体：</label>
			<div class="form-control-static"><pre id="responseStr2"></pre></div>
		</div>
		<div class="form-group">
			<label class="col-sm-2 control-label">一级是否成功：</label>
			<div class="form-control-static" th:class="${cusRequestHistory.successFlag1 == 'Y' ? 'label label-primary' : 'label label-danger'}" th:text="${cusRequestHistory.successFlag1 == 'Y' ? '成功' : '失败'}">
			</div>
		</div>
		<div class="form-group">
			<label class="col-sm-2 control-label">二级是否成功：</label>
			<div class="form-control-static" th:class="${cusRequestHistory.successFlag2 == 'Y' ? 'label label-primary' : 'label label-danger'}" th:text="${cusRequestHistory.successFlag2 == 'Y' ? '成功' : '失败'}">
			</div>
		</div>
		<div class="form-group">
			<label class="col-sm-2 control-label">总体是否成功：</label>
			<div class="form-control-static" th:class="${cusRequestHistory.totalFlag == 'Y' ? 'label label-primary' : 'label label-danger'}" th:text="${cusRequestHistory.totalFlag == 'Y' ? '成功' : '失败'}">
			</div>
		</div>
	</form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: jsonview-js" />
    <script th:inline="javascript">
	    $(function() {
	    	var requestStr = [[${cusRequestHistory.requestStr}]];
	    	if ($.common.isNotEmpty(requestStr) && requestStr.length < 2000) {
	    		$("#requestParam").JSONView(requestStr);
	    	} else {
	    		$("#requestParam").text(requestStr);
	    	}
	    	var responseStr1 = [[${cusRequestHistory.responseStr1}]];
	    	if ($.common.isNotEmpty(responseStr1) && responseStr1.length < 2000) {
	    		$("#responseStr1").JSONView(responseStr1);
	    	} else {
	    		$("#responseStr1").text(responseStr1);
	    	}
	    	var responseStr2 = [[${cusRequestHistory.responseStr2}]];
	    	if ($.common.isNotEmpty(responseStr2) && responseStr2.length < 2000) {
	    		$("#responseStr2").JSONView(responseStr2);
	    	} else {
	    		$("#responseStr2").text(responseStr2);
	    	}
	    });
    </script>
</body>
</html>
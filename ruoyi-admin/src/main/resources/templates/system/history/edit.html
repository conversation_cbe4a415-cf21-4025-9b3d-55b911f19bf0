<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改接口请求记录')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-history-edit" th:object="${cusRequestHistory}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">请求体：</label>
                    <div class="col-sm-8">
                        <textarea name="requestStr" class="form-control">[[*{requestStr}]]</textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">一级响应体：</label>
                    <div class="col-sm-8">
                        <textarea name="responseStr1" class="form-control">[[*{responseStr1}]]</textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">二级响应体：</label>
                    <div class="col-sm-8">
                        <textarea name="responseStr2" class="form-control">[[*{responseStr2}]]</textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">一级是否成功：</label>
                    <div class="col-sm-8">
                        <input name="successFlag1" th:field="*{successFlag1}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">二级是否成功：</label>
                    <div class="col-sm-8">
                        <input name="successFlag2" th:field="*{successFlag2}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">总体是否成功：</label>
                    <div class="col-sm-8">
                        <input name="totalFlag" th:field="*{totalFlag}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">请求id：</label>
                    <div class="col-sm-8">
                        <input name="requestId" th:field="*{requestId}" class="form-control" type="text">
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "system/history";
        $("#form-history-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-history-edit').serialize());
            }
        }
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <title>登录高飞易微派系统</title>
    <meta name="description" content="高飞易微派管理系统">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="shortcut icon" href="../static/favicon.ico" th:href="@{favicon.ico}"/>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 900px;
            min-height: 500px;
            display: flex;
            animation: slideUp 0.6s ease-out;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .login-left {
            flex: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
        }
        
        .login-left::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
            opacity: 0.3;
        }
        
        .login-left > * {
            position: relative;
            z-index: 1;
        }
        
        .logo {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .welcome-text {
            font-size: 1.2rem;
            line-height: 1.6;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .features {
            list-style: none;
        }
        
        .features li {
            padding: 8px 0;
            font-size: 1rem;
            opacity: 0.8;
        }
        
        .features li::before {
            content: '✓';
            margin-right: 10px;
            color: #4ade80;
            font-weight: bold;
        }
        
        .login-right {
            flex: 1;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .login-form h2 {
            color: #1f2937;
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .login-form p {
            color: #6b7280;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-control {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f9fafb;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .captcha-row {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .captcha-input {
            flex: 1;
        }
        
        .captcha-img {
            height: 50px;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        
        .captcha-img:hover {
            transform: scale(1.05);
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            margin: 20px 0;
        }
        
        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
        }
        
        .checkbox-group label {
            color: #6b7280;
            cursor: pointer;
        }
        
        .btn-login {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-login:active {
            transform: translateY(0);
        }
        
        .btn-login:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }
        
        .error-message {
            color: #ef4444;
            font-size: 0.875rem;
            margin-top: 5px;
            display: none;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .login-container {
                flex-direction: column;
                max-width: 400px;
                margin: 10px;
            }
            
            .login-left {
                padding: 40px 30px;
                text-align: center;
            }
            
            .login-right {
                padding: 40px 30px;
            }
            
            .logo {
                font-size: 2rem;
            }
            
            .captcha-row {
                flex-direction: column;
                gap: 10px;
            }
            
            .captcha-img {
                width: 100%;
                max-width: 200px;
            }
        }
        
        @media (max-width: 480px) {
            body {
                padding: 10px;
            }
            
            .login-left,
            .login-right {
                padding: 30px 20px;
            }
        }
    </style>
    <script>
        if(window.top!==window.self){alert('未登录或登录超时。请重新登录');window.top.location=window.location};
    </script>
</head>
<body>
    <div class="login-container">
        <div class="login-left">
            <div class="logo">高飞易微派</div>
            <div class="welcome-text">欢迎使用高飞易微派管理系统</div>
            <ul class="features">
                <li>现代化界面设计</li>
                <li>安全可靠的认证</li>
                <li>响应式布局</li>
                <li>高效的管理体验</li>
            </ul>
        </div>
        
        <div class="login-right">
            <form id="loginForm" class="login-form" autocomplete="off">
                <h2>登录</h2>
                <p>请输入您的账户信息</p>
                
                <div class="form-group">
                    <input type="text" name="username" class="form-control" placeholder="用户名" value="admin" required>
                    <div class="error-message" id="username-error"></div>
                </div>
                
                <div class="form-group">
                    <input type="password" name="password" class="form-control" placeholder="密码" required>
                    <div class="error-message" id="password-error"></div>
                </div>
                
                <div class="form-group" th:if="${captchaEnabled==true}">
                    <div class="captcha-row">
                        <input type="text" name="validateCode" class="form-control captcha-input" placeholder="验证码" maxlength="5">
                        <img th:src="@{/captcha/captchaImage(type=${captchaType})}" class="captcha-img" id="captchaImg" title="点击更换验证码" alt="验证码">
                    </div>
                    <div class="error-message" id="captcha-error"></div>
                </div>
                
                <div class="checkbox-group" th:if="${isRemembered}">
                    <input type="checkbox" id="rememberme" name="rememberme">
                    <label for="rememberme">记住我</label>
                </div>
                
                <button type="submit" class="btn-login" id="loginBtn">
                    <span id="loginText">登录</span>
                </button>
            </form>
        </div>
    </div>
    
    <script th:inline="javascript">
        var ctx = /*[[${#httpServletRequest.contextPath}]]*/ '';
        var captchaType = /*[[${captchaType}]]*/ '';
        var captchaEnabled = /*[[${captchaEnabled}]]*/ false;
        
        document.addEventListener('DOMContentLoaded', function() {
            initLogin();
        });
        
        function initLogin() {
            validateKickout();
            bindEvents();
        }
        
        function bindEvents() {
            // 表单提交
            document.getElementById('loginForm').addEventListener('submit', function(e) {
                e.preventDefault();
                login();
            });
            
            // 验证码点击刷新
            var captchaImg = document.getElementById('captchaImg');
            if (captchaImg) {
                captchaImg.addEventListener('click', function() {
                    refreshCaptcha();
                });
            }
            
            // 输入框焦点事件
            var inputs = document.querySelectorAll('.form-control');
            inputs.forEach(function(input) {
                input.addEventListener('focus', function() {
                    clearError(this.name);
                });
            });
        }
        
        function login() {
            var form = document.getElementById('loginForm');
            var formData = new FormData(form);
            var username = formData.get('username').trim();
            var password = formData.get('password').trim();
            var validateCode = formData.get('validateCode') || '';
            var rememberMe = formData.get('rememberme') === 'on';
            
            // 基础验证
            if (!username) {
                showError('username', '请输入用户名');
                return;
            }
            
            if (!password) {
                showError('password', '请输入密码');
                return;
            }
            
            if (captchaEnabled && !validateCode) {
                showError('captcha', '请输入验证码');
                return;
            }
            
            // 显示加载状态
            setLoading(true);
            
            // 发送登录请求
            var xhr = new XMLHttpRequest();
            xhr.open('POST', ctx + '/login', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    setLoading(false);
                    
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.code === 0) {
                                window.location.href = ctx + '/index';
                            } else {
                                showError('captcha', response.msg || '登录失败');
                                refreshCaptcha();
                                clearInput('validateCode');
                            }
                        } catch (e) {
                            showError('captcha', '登录请求异常');
                        }
                    } else {
                        showError('captcha', '网络请求失败');
                    }
                }
            };
            
            var params = 'username=' + encodeURIComponent(username) +
                        '&password=' + encodeURIComponent(password) +
                        '&validateCode=' + encodeURIComponent(validateCode) +
                        '&rememberMe=' + rememberMe;
            
            xhr.send(params);
        }
        
        function refreshCaptcha() {
            var captchaImg = document.getElementById('captchaImg');
            if (captchaImg) {
                var url = ctx + '/captcha/captchaImage?type=' + captchaType + '&s=' + Math.random();
                captchaImg.src = url;
            }
        }
        
        function setLoading(loading) {
            var btn = document.getElementById('loginBtn');
            var text = document.getElementById('loginText');
            
            if (loading) {
                btn.disabled = true;
                text.innerHTML = '<span class="loading"></span>登录中...';
            } else {
                btn.disabled = false;
                text.innerHTML = '登录';
            }
        }
        
        function showError(field, message) {
            var errorElement = document.getElementById(field + '-error');
            if (errorElement) {
                errorElement.textContent = message;
                errorElement.style.display = 'block';
            }
        }
        
        function clearError(field) {
            var errorElement = document.getElementById(field + '-error');
            if (errorElement) {
                errorElement.style.display = 'none';
            }
        }
        
        function clearInput(name) {
            var input = document.querySelector('input[name="' + name + '"]');
            if (input) {
                input.value = '';
            }
        }
        
        function validateKickout() {
            var kickout = getUrlParam('kickout');
            if (kickout === '1') {
                alert('您已在别处登录，请您修改密码或重新登录');
                if (window.top !== window.self) {
                    window.top.location = window.self.location;
                } else {
                    var url = window.location.search;
                    if (url) {
                        var oldUrl = window.location.href;
                        var newUrl = oldUrl.substring(0, oldUrl.indexOf('?'));
                        window.location = newUrl;
                    }
                }
            }
        }
        
        function getUrlParam(name) {
            var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return decodeURIComponent(r[2]);
            return null;
        }
    </script>
</body>
</html>

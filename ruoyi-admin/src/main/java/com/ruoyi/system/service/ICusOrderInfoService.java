package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.CusOrderInfo;
import com.ruoyi.system.domain.CusReceiveHistory;

/**
 * 订单信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
public interface ICusOrderInfoService 
{
    /**
     * 查询订单信息
     * 
     * @param id 订单信息主键
     * @return 订单信息
     */
    public CusOrderInfo selectCusOrderInfoById(Long id);

    /**
     * 查询订单信息列表
     * 
     * @param cusOrderInfo 订单信息
     * @return 订单信息集合
     */
    public List<CusOrderInfo> selectCusOrderInfoList(CusOrderInfo cusOrderInfo);

    /**
     * 新增订单信息
     * 
     * @param cusOrderInfo 订单信息
     * @return 结果
     */
    public int insertCusOrderInfo(CusOrderInfo cusOrderInfo);

    /**
     * 修改订单信息
     * 
     * @param cusOrderInfo 订单信息
     * @return 结果
     */
    public int updateCusOrderInfo(CusOrderInfo cusOrderInfo);

    /**
     * 批量删除订单信息
     * 
     * @param ids 需要删除的订单信息主键集合
     * @return 结果
     */
    public int deleteCusOrderInfoByIds(String ids);

    /**
     * 删除订单信息信息
     * 
     * @param id 订单信息主键
     * @return 结果
     */
    public int deleteCusOrderInfoById(Long id);

    public void importUser(List<CusOrderInfo> userList);

    public void touchCall();

    public int touchCallByIds(String ids);

    public void dealPic(List<String> finalList, CusReceiveHistory cusReceiveHistory);
}

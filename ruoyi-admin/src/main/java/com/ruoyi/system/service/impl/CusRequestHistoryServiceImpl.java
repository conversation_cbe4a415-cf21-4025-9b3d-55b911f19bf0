package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CusRequestHistoryMapper;
import com.ruoyi.system.domain.CusRequestHistory;
import com.ruoyi.system.service.ICusRequestHistoryService;
import com.ruoyi.common.core.text.Convert;

/**
 * 接口请求记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
@Service
public class CusRequestHistoryServiceImpl implements ICusRequestHistoryService 
{
    @Autowired
    private CusRequestHistoryMapper cusRequestHistoryMapper;

    /**
     * 查询接口请求记录
     * 
     * @param id 接口请求记录主键
     * @return 接口请求记录
     */
    @Override
    public CusRequestHistory selectCusRequestHistoryById(Long id)
    {
        return cusRequestHistoryMapper.selectCusRequestHistoryById(id);
    }

    /**
     * 查询接口请求记录列表
     * 
     * @param cusRequestHistory 接口请求记录
     * @return 接口请求记录
     */
    @Override
    public List<CusRequestHistory> selectCusRequestHistoryList(CusRequestHistory cusRequestHistory)
    {
        return cusRequestHistoryMapper.selectCusRequestHistoryList(cusRequestHistory);
    }

    /**
     * 新增接口请求记录
     * 
     * @param cusRequestHistory 接口请求记录
     * @return 结果
     */
    @Override
    public int insertCusRequestHistory(CusRequestHistory cusRequestHistory)
    {
        cusRequestHistory.setCreateTime(DateUtils.getNowDate());
        return cusRequestHistoryMapper.insertCusRequestHistory(cusRequestHistory);
    }

    /**
     * 修改接口请求记录
     * 
     * @param cusRequestHistory 接口请求记录
     * @return 结果
     */
    @Override
    public int updateCusRequestHistory(CusRequestHistory cusRequestHistory)
    {
        cusRequestHistory.setUpdateTime(DateUtils.getNowDate());
        return cusRequestHistoryMapper.updateCusRequestHistory(cusRequestHistory);
    }

    /**
     * 批量删除接口请求记录
     * 
     * @param ids 需要删除的接口请求记录主键
     * @return 结果
     */
    @Override
    public int deleteCusRequestHistoryByIds(String ids)
    {
        return cusRequestHistoryMapper.deleteCusRequestHistoryByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除接口请求记录信息
     * 
     * @param id 接口请求记录主键
     * @return 结果
     */
    @Override
    public int deleteCusRequestHistoryById(Long id)
    {
        return cusRequestHistoryMapper.deleteCusRequestHistoryById(id);
    }
}

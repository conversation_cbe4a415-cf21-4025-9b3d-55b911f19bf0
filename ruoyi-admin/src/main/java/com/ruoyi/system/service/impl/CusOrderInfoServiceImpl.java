package com.ruoyi.system.service.impl;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.file.ImageUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.system.domain.CusOrderInfo;
import com.ruoyi.system.domain.CusReceiveHistory;
import com.ruoyi.system.domain.CusRequestHistory;
import com.ruoyi.system.mapper.CusOrderInfoMapper;
import com.ruoyi.system.service.ICusOrderInfoService;
import com.ruoyi.system.service.ICusRequestHistoryService;
import com.ruoyi.web.forest.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.util.List;

/**
 * 订单信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-03
 */
@Service
@Slf4j
public class CusOrderInfoServiceImpl implements ICusOrderInfoService {
    @Autowired
    private CusOrderInfoMapper cusOrderInfoMapper;

    @Resource
    private SfexpressService sfexpressService;
    @Resource
    private ICusRequestHistoryService cusRequestHistoryService;

    @Value("${cus.partnerId:GFY}")
    private String partnerId;

    @Value("${cus.checkWord:4Q9DVYV78NUD0MCI}")
    private String checkWord;

    @Value("${cus.Url:https://sfapi.sit.sf-express.com:45273/std/service}")
    private String realUrl;

    /**
     * 查询订单信息
     *
     * @param id 订单信息主键
     * @return 订单信息
     */
    @Override
    public CusOrderInfo selectCusOrderInfoById(Long id) {
        return cusOrderInfoMapper.selectCusOrderInfoById(id);
    }

    /**
     * 查询订单信息列表
     *
     * @param cusOrderInfo 订单信息
     * @return 订单信息
     */
    @Override
    public List<CusOrderInfo> selectCusOrderInfoList(CusOrderInfo cusOrderInfo) {
        return cusOrderInfoMapper.selectCusOrderInfoList(cusOrderInfo);
    }

    /**
     * 新增订单信息
     *
     * @param cusOrderInfo 订单信息
     * @return 结果
     */
    @Override
    public int insertCusOrderInfo(CusOrderInfo cusOrderInfo) {
        cusOrderInfo.setCreateTime(DateUtils.getNowDate());
        return cusOrderInfoMapper.insertCusOrderInfo(cusOrderInfo);
    }

    /**
     * 修改订单信息
     *
     * @param cusOrderInfo 订单信息
     * @return 结果
     */
    @Override
    public int updateCusOrderInfo(CusOrderInfo cusOrderInfo) {
        cusOrderInfo.setUpdateTime(DateUtils.getNowDate());
        return cusOrderInfoMapper.updateCusOrderInfo(cusOrderInfo);
    }

    /**
     * 批量删除订单信息
     *
     * @param ids 需要删除的订单信息主键
     * @return 结果
     */
    @Override
    public int deleteCusOrderInfoByIds(String ids) {
        return cusOrderInfoMapper.deleteCusOrderInfoByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除订单信息信息
     *
     * @param id 订单信息主键
     * @return 结果
     */
    @Override
    public int deleteCusOrderInfoById(Long id) {
        return cusOrderInfoMapper.deleteCusOrderInfoById(id);
    }

    //导入
    public void importUser(List<CusOrderInfo> userList) {
        for (CusOrderInfo user : userList) {
            user.setCreateTime(DateUtils.getNowDate());
            cusOrderInfoMapper.insertCusOrderInfo(user);
        }
    }

    public void touchCall() {
        List<CusOrderInfo> cusOrderInfos = cusOrderInfoMapper.selectCusOrderInfoListToCall();
        for (CusOrderInfo cusOrderInfo : cusOrderInfos) {
            this.sendRequest(cusOrderInfo);
        }
    }

    public int touchCallByIds(String ids) {
        List<CusOrderInfo> cusOrderInfos = cusOrderInfoMapper.selectCusOrderInfoListToCallByIds(Convert.toStrArray(ids));
        for (CusOrderInfo cusOrderInfo : cusOrderInfos) {
            this.sendRequest(cusOrderInfo);
        }
        return cusOrderInfos.size();
    }


    @Transactional
    public void sendRequest(CusOrderInfo cusOrderInfo) {
        String waybillNo = cusOrderInfo.getWaybillNo();
        String checkPhoneNo = cusOrderInfo.getCheckPhoneNo();
        String checkMonthCard = cusOrderInfo.getCheckMonthCard();
        String subsidySource = cusOrderInfo.getSubsidySource();
        String wpMerchantCode = cusOrderInfo.getWpMerchantCode();
        String wpServiceCode = cusOrderInfo.getWpServiceCode();
        String snCode = cusOrderInfo.getSnCode();
        String imeiCode = cusOrderInfo.getImelCode();
        String cargoName = cusOrderInfo.getCargoName();
        SfexpressRequestDto sfexpressRequestDto = new SfexpressRequestDto();
        SfMsgDataDto sfMsgDataDto = new SfMsgDataDto();
        List<ExtralnfoListDTO> extralnfoListDTOS = sfMsgDataDto.buildExtra(subsidySource, wpMerchantCode, wpServiceCode, snCode, imeiCode, cargoName, null);
        sfMsgDataDto.setExtralnfoListDTOList(extralnfoListDTOS);
        sfMsgDataDto.setWaybillNo(waybillNo);
        sfMsgDataDto.setCheckPhoneNo(checkPhoneNo);
        sfMsgDataDto.setCheckMonthCard(checkMonthCard);
        sfexpressRequestDto.setPartnerID(partnerId);
        String requestId = UUID.fastUUID().toString(true);
        sfexpressRequestDto.setRequestId(requestId);
        sfexpressRequestDto.setServiceCode("EXP_RECE_SUBSIDY_ORDER");
        long timestamp = System.currentTimeMillis();
        sfexpressRequestDto.setTimestamp("" + timestamp);
        String jsonString = JSON.toJSONString(sfMsgDataDto);
        sfexpressRequestDto.setMsgData(jsonString);
        sfexpressRequestDto.setMsgDigest("" + this.getCheckWord(jsonString, "" + timestamp, checkWord));

        SfExpressResponse jsonObject = sfexpressService.sendRequest(realUrl, sfexpressRequestDto);
        if (StringUtils.isNotBlank(jsonObject.getApiResultCode()) && jsonObject.getApiResultCode().equals("A1000")) {
            SfExpressFinalResponse sfExpressFinalResponse = JSON.parseObject(jsonObject.getApiResultData(), SfExpressFinalResponse.class);
            if (ObjectUtil.isNotEmpty(sfExpressFinalResponse)) {
                CusRequestHistory cusRequestHistory = new CusRequestHistory();
                cusRequestHistory.setRequestStr(JSON.toJSONString(sfexpressRequestDto));
                cusRequestHistory.setResponseStr1(JSON.toJSONString(jsonObject));
                cusRequestHistory.setSuccessFlag1(jsonObject.isSuccess() ? "Y" : "N");
                cusRequestHistory.setResponseStr2(JSON.toJSONString(sfExpressFinalResponse));
                cusRequestHistory.setSuccessFlag2(sfExpressFinalResponse.isSuccess() ? "Y" : "N");
                cusRequestHistory.setTotalFlag(cusRequestHistory.getSuccessFlag1().equals("Y") && cusRequestHistory.getSuccessFlag2().equals("Y") ? "Y" : "N");
                cusRequestHistory.setRequestId(requestId);
                cusRequestHistory.setCreateBy("admin");
                cusRequestHistory.setUpdateBy("admin");
                cusRequestHistory.setRemark("");
                cusRequestHistoryService.insertCusRequestHistory(cusRequestHistory);
                cusOrderInfo.setFinalResult(cusRequestHistory.getTotalFlag());
                cusOrderInfo.setLogId(requestId);
                cusOrderInfo.setUpdateTime(DateUtils.getNowDate());
                cusOrderInfoMapper.updateCusOrderInfo(cusOrderInfo);
            } else {
                this.dealErrorLog(jsonObject, sfExpressFinalResponse, sfexpressRequestDto, requestId, cusOrderInfo);
            }
        } else {
            this.dealErrorLog(jsonObject, null, sfexpressRequestDto, requestId, cusOrderInfo);
        }
    }

    public void dealErrorLog(SfExpressResponse jsonObject, SfExpressFinalResponse sfExpressFinalResponse, SfexpressRequestDto sfexpressRequestDto, String requestId, CusOrderInfo cusOrderInfo) {
        CusRequestHistory cusRequestHistory = new CusRequestHistory();
        cusRequestHistory.setRequestStr(JSON.toJSONString(sfexpressRequestDto));
        cusRequestHistory.setResponseStr1(JSON.toJSONString(jsonObject));
        cusRequestHistory.setSuccessFlag1(jsonObject.isSuccess() ? "Y" : "N");
        cusRequestHistory.setResponseStr2(sfExpressFinalResponse != null ? JSON.toJSONString(sfExpressFinalResponse) : "二级响应为空");
        cusRequestHistory.setSuccessFlag2(sfExpressFinalResponse != null ? (sfExpressFinalResponse.isSuccess() ? "Y" : "N") : "N");
        cusRequestHistory.setTotalFlag(cusRequestHistory.getSuccessFlag1().equals("Y") && cusRequestHistory.getSuccessFlag2().equals("Y") ? "Y" : "N");
        cusRequestHistory.setRequestId(requestId);
        cusRequestHistory.setCreateBy("admin");
        cusRequestHistory.setUpdateBy("admin");
        cusRequestHistory.setRemark("");
        cusRequestHistoryService.insertCusRequestHistory(cusRequestHistory);
        cusOrderInfo.setFinalResult(cusRequestHistory.getTotalFlag());
        cusOrderInfo.setLogId(requestId);
        cusOrderInfo.setUpdateTime(DateUtils.getNowDate());
        cusOrderInfoMapper.updateCusOrderInfo(cusOrderInfo);
    }

    //工具方法，按顺序拼接：msgData（业务报文）+ timestamp+checkWord；经过UTF-8后进行MD5，最后在转换为Base64字符串;
    @SneakyThrows
    private String getCheckWord(String msgData, String timestamp, String checkWord) {
        String toVerify = msgData + timestamp + checkWord;
        toVerify = URLEncoder.encode(toVerify, "UTF-8");
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        md5.update(toVerify.getBytes("UTF-8"));
        byte[] digest = md5.digest();
        String s1 = new String(Base64Encoder.encode(digest));
        return s1;
    }

    @Async("dealLocalPic")
    public void dealPic(List<String> finalList, CusReceiveHistory cusReceiveHistory) {
        List<String> localPhotoList = Lists.newArrayList();
        //处理图片下载及本地存储
        for (String photoUrl : finalList) {
            byte[] bytes = ImageUtils.readFile(photoUrl);
            try {
                String s = FileUtils.writeBytes(bytes, RuoYiConfig.getProfile());
                localPhotoList.add(s);
            } catch (Exception e) {
                e.printStackTrace();
                continue;
            }
        }
        if (CollectionUtil.isNotEmpty(localPhotoList)) {
            CusOrderInfo cusOrderInfo = new CusOrderInfo();
            cusOrderInfo.setLocalPhotoList(String.join(",", localPhotoList));
            cusOrderInfo.setWaybillNo(cusReceiveHistory.getWayBillNo());
            cusOrderInfoMapper.updateCusOrderInfoByWaybill(cusOrderInfo);
            log.info("本地图片处理完成，共{}个", localPhotoList.size());
        } else {
            log.info("本地图片处理失败");
        }

    }
}

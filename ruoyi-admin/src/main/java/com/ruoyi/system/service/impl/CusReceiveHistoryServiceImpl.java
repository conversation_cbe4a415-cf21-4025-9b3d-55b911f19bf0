package com.ruoyi.system.service.impl;

import cn.hutool.core.img.ImgUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.file.ImageUtils;
import com.ruoyi.system.domain.CusReceiveHistory;
import com.ruoyi.system.dto.RequestBySfexpressDto;
import com.ruoyi.system.mapper.CusReceiveHistoryMapper;
import com.ruoyi.system.service.ICusOrderInfoService;
import com.ruoyi.system.service.ICusReceiveHistoryService;
import com.ruoyi.system.util.AES256CipherExternal;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 接口响应记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-06
 */
@Slf4j
@Service
public class CusReceiveHistoryServiceImpl implements ICusReceiveHistoryService {
    @Autowired
    private CusReceiveHistoryMapper cusReceiveHistoryMapper;

    @Resource
    private ICusOrderInfoService cusOrderInfoService;

    @Value("${cus.partnerId:Gaofeiyi}")
    private String partnerId;

    @Value("${cus.checkWord:93558LGRXSUP}")
    private String checkWord;

    /**
     * 查询接口响应记录
     *
     * @param id 接口响应记录主键
     * @return 接口响应记录
     */
    @Override
    public CusReceiveHistory selectCusReceiveHistoryById(Long id) {
        return cusReceiveHistoryMapper.selectCusReceiveHistoryById(id);
    }

    /**
     * 查询接口响应记录列表
     *
     * @param cusReceiveHistory 接口响应记录
     * @return 接口响应记录
     */
    @Override
    public List<CusReceiveHistory> selectCusReceiveHistoryList(CusReceiveHistory cusReceiveHistory) {
        return cusReceiveHistoryMapper.selectCusReceiveHistoryList(cusReceiveHistory);
    }

    /**
     * 新增接口响应记录
     *
     * @param cusReceiveHistory 接口响应记录
     * @return 结果
     */
    @Override
    public int insertCusReceiveHistory(CusReceiveHistory cusReceiveHistory) {
        cusReceiveHistory.setCreateTime(DateUtils.getNowDate());
        return cusReceiveHistoryMapper.insertCusReceiveHistory(cusReceiveHistory);
    }

    /**
     * 修改接口响应记录
     *
     * @param cusReceiveHistory 接口响应记录
     * @return 结果
     */
    @Override
    public int updateCusReceiveHistory(CusReceiveHistory cusReceiveHistory) {
        cusReceiveHistory.setUpdateTime(DateUtils.getNowDate());
        return cusReceiveHistoryMapper.updateCusReceiveHistory(cusReceiveHistory);
    }

    /**
     * 批量删除接口响应记录
     *
     * @param ids 需要删除的接口响应记录主键
     * @return 结果
     */
    @Override
    public int deleteCusReceiveHistoryByIds(String ids) {
        return cusReceiveHistoryMapper.deleteCusReceiveHistoryByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除接口响应记录信息
     *
     * @param id 接口响应记录主键
     * @return 结果
     */
    @Override
    public int deleteCusReceiveHistoryById(Long id) {
        return cusReceiveHistoryMapper.deleteCusReceiveHistoryById(id);
    }

    @Async("dealPic")
    public void asyncDealPhoto(Long logId) {
        CusReceiveHistory cusReceiveHistory = this.selectCusReceiveHistoryById(logId);
        RequestBySfexpressDto javaObject = JSON.toJavaObject(JSON.parseObject(cusReceiveHistory.getMsgData()), RequestBySfexpressDto.class);
        AES256CipherExternal aes256 = new AES256CipherExternal("sncodedt23ewfpro");
        List<String> finalList = Lists.newArrayList();
        for (RequestBySfexpressDto.PhotoListDTO photoListDTO : javaObject.getPhotoList()) {
            try {
                String s = aes256.AES_Decode(photoListDTO.getPhotoUrl());
                finalList.add(s);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        cusReceiveHistory.setPhotoList(String.join(",", finalList));
        cusReceiveHistoryMapper.updateCusReceiveHistory(cusReceiveHistory);
        cusOrderInfoService.dealPic(finalList, cusReceiveHistory);
    }

}

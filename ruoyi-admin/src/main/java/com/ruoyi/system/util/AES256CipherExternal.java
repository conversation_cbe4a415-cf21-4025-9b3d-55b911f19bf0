package com.ruoyi.system.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.spec.AlgorithmParameterSpec;

/**
 * Description: TODO
 *
 * <AUTHOR>
 * @date 2025/1/15 11:15
 */
@Slf4j
public class AES256CipherExternal {

    protected static final byte[] ivBytes = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00};

    private Cipher cipherEncrypt = null;
    private Cipher cipherDncrypt = null;

    public AES256CipherExternal(String key) {
        try {
            AlgorithmParameterSpec ivSpec = new IvParameterSpec(ivBytes);
            SecretKeySpec newKey = new SecretKeySpec(key.getBytes("UTF-8"), "AES");
            cipherEncrypt = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipherEncrypt.init(Cipher.ENCRYPT_MODE, newKey, ivSpec);

            cipherDncrypt = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipherDncrypt.init(Cipher.DECRYPT_MODE, newKey, ivSpec);
        } catch (Exception e) {
            log.warn("AES256CipherExternal init Fail,key:" + key, e);
        }
    }

    /**
     * 加密
     *
     * @param str
     * @return
     * @throws Exception
     */
    public String AES_Encode(String str) throws Exception {
        byte[] textBytes = str.getBytes("UTF-8");

        return Base64.encodeBase64String(cipherEncrypt.doFinal(textBytes));
    }

    public byte[] AES_Encode(byte[] textBytes) throws Exception {

        return cipherEncrypt.doFinal(textBytes);
    }

    public String AES_Decode(String str) throws Exception {

        byte[] textBytes = Base64.decodeBase64(str);
        // byte[] textBytes = str.getBytes("UTF-8");
        return new String(cipherDncrypt.doFinal(textBytes), "UTF-8");
    }

    public byte[] AES_Decode(byte[] textBytes) throws Exception {

        return cipherDncrypt.doFinal(textBytes);
    }

}

package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.CusReceiveHistory;

/**
 * 接口响应记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-06
 */
public interface CusReceiveHistoryMapper 
{
    /**
     * 查询接口响应记录
     * 
     * @param id 接口响应记录主键
     * @return 接口响应记录
     */
    public CusReceiveHistory selectCusReceiveHistoryById(Long id);

    /**
     * 查询接口响应记录列表
     * 
     * @param cusReceiveHistory 接口响应记录
     * @return 接口响应记录集合
     */
    public List<CusReceiveHistory> selectCusReceiveHistoryList(CusReceiveHistory cusReceiveHistory);

    /**
     * 新增接口响应记录
     * 
     * @param cusReceiveHistory 接口响应记录
     * @return 结果
     */
    public int insertCusReceiveHistory(CusReceiveHistory cusReceiveHistory);

    /**
     * 修改接口响应记录
     * 
     * @param cusReceiveHistory 接口响应记录
     * @return 结果
     */
    public int updateCusReceiveHistory(CusReceiveHistory cusReceiveHistory);

    /**
     * 删除接口响应记录
     * 
     * @param id 接口响应记录主键
     * @return 结果
     */
    public int deleteCusReceiveHistoryById(Long id);

    /**
     * 批量删除接口响应记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCusReceiveHistoryByIds(String[] ids);
}

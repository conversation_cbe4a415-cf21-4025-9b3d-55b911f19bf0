package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.CusRequestHistory;

/**
 * 接口请求记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
public interface CusRequestHistoryMapper 
{
    /**
     * 查询接口请求记录
     * 
     * @param id 接口请求记录主键
     * @return 接口请求记录
     */
    public CusRequestHistory selectCusRequestHistoryById(Long id);

    /**
     * 查询接口请求记录列表
     * 
     * @param cusRequestHistory 接口请求记录
     * @return 接口请求记录集合
     */
    public List<CusRequestHistory> selectCusRequestHistoryList(CusRequestHistory cusRequestHistory);

    /**
     * 新增接口请求记录
     * 
     * @param cusRequestHistory 接口请求记录
     * @return 结果
     */
    public int insertCusRequestHistory(CusRequestHistory cusRequestHistory);

    /**
     * 修改接口请求记录
     * 
     * @param cusRequestHistory 接口请求记录
     * @return 结果
     */
    public int updateCusRequestHistory(CusRequestHistory cusRequestHistory);

    /**
     * 删除接口请求记录
     * 
     * @param id 接口请求记录主键
     * @return 结果
     */
    public int deleteCusRequestHistoryById(Long id);

    /**
     * 批量删除接口请求记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCusRequestHistoryByIds(String[] ids);
}

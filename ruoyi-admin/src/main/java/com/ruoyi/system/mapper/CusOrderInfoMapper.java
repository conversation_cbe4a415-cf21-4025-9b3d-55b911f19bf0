package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.CusOrderInfo;

/**
 * 订单信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
public interface CusOrderInfoMapper 
{
    /**
     * 查询订单信息
     * 
     * @param id 订单信息主键
     * @return 订单信息
     */
    public CusOrderInfo selectCusOrderInfoById(Long id);

    /**
     * 查询订单信息列表
     * 
     * @param cusOrderInfo 订单信息
     * @return 订单信息集合
     */
    public List<CusOrderInfo> selectCusOrderInfoList(CusOrderInfo cusOrderInfo);

    public List<CusOrderInfo> selectCusOrderInfoListToCall();

    public List<CusOrderInfo> selectCusOrderInfoListToCallByIds(String[] ids);

    /**
     * 新增订单信息
     * 
     * @param cusOrderInfo 订单信息
     * @return 结果
     */
    public int insertCusOrderInfo(CusOrderInfo cusOrderInfo);

    /**
     * 修改订单信息
     * 
     * @param cusOrderInfo 订单信息
     * @return 结果
     */
    public int updateCusOrderInfo(CusOrderInfo cusOrderInfo);

    public int updateCusOrderInfoByWaybill(CusOrderInfo cusOrderInfo);

    /**
     * 删除订单信息
     * 
     * @param id 订单信息主键
     * @return 结果
     */
    public int deleteCusOrderInfoById(Long id);

    /**
     * 批量删除订单信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCusOrderInfoByIds(String[] ids);
}

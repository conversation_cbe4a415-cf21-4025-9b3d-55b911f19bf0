package com.ruoyi.system.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.CusReceiveHistory;
import com.ruoyi.system.service.ICusReceiveHistoryService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 接口响应记录Controller
 * 
 * <AUTHOR>
 * @date 2025-09-06
 */
@Controller
@RequestMapping("/system/receiveHistory")
public class CusReceiveHistoryController extends BaseController
{
    private String prefix = "system/receiveHistory";

    @Autowired
    private ICusReceiveHistoryService cusReceiveHistoryService;

    @RequiresPermissions("system:receiveHistory:view")
    @GetMapping()
    public String receiveHistory()
    {
        return prefix + "/receiveHistory";
    }

    /**
     * 查询接口响应记录列表
     */
    @RequiresPermissions("system:receiveHistory:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(CusReceiveHistory cusReceiveHistory)
    {
        startPage();
        List<CusReceiveHistory> list = cusReceiveHistoryService.selectCusReceiveHistoryList(cusReceiveHistory);
        return getDataTable(list);
    }

    /**
     * 导出接口响应记录列表
     */
    @RequiresPermissions("system:receiveHistory:export")
    @Log(title = "接口响应记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(CusReceiveHistory cusReceiveHistory)
    {
        List<CusReceiveHistory> list = cusReceiveHistoryService.selectCusReceiveHistoryList(cusReceiveHistory);
        ExcelUtil<CusReceiveHistory> util = new ExcelUtil<CusReceiveHistory>(CusReceiveHistory.class);
        return util.exportExcel(list, "接口响应记录数据");
    }

    /**
     * 新增接口响应记录
     */
    @RequiresPermissions("system:receiveHistory:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存接口响应记录
     */
    @RequiresPermissions("system:receiveHistory:add")
    @Log(title = "接口响应记录", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(CusReceiveHistory cusReceiveHistory)
    {
        return toAjax(cusReceiveHistoryService.insertCusReceiveHistory(cusReceiveHistory));
    }

    /**
     * 修改接口响应记录
     */
    @RequiresPermissions("system:receiveHistory:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        CusReceiveHistory cusReceiveHistory = cusReceiveHistoryService.selectCusReceiveHistoryById(id);
        mmap.put("cusReceiveHistory", cusReceiveHistory);
        return prefix + "/edit";
    }

    /**
     * 修改保存接口响应记录
     */
    @RequiresPermissions("system:receiveHistory:edit")
    @Log(title = "接口响应记录", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(CusReceiveHistory cusReceiveHistory)
    {
        return toAjax(cusReceiveHistoryService.updateCusReceiveHistory(cusReceiveHistory));
    }

    /**
     * 删除接口响应记录
     */
    @RequiresPermissions("system:receiveHistory:remove")
    @Log(title = "接口响应记录", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(cusReceiveHistoryService.deleteCusReceiveHistoryByIds(ids));
    }
}

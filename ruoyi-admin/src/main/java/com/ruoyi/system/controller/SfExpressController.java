package com.ruoyi.system.controller;

import cn.hutool.core.codec.Base64Encoder;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.system.domain.CusReceiveHistory;
import com.ruoyi.system.domain.CusRequestHistory;
import com.ruoyi.system.dto.RequestBySfexpressDto;
import com.ruoyi.system.dto.ResponseToSfexpressDto;
import com.ruoyi.system.service.ICusReceiveHistoryService;
import com.ruoyi.system.service.ICusRequestHistoryService;
import com.ruoyi.system.util.AES256CipherExternal;
import com.ruoyi.web.forest.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresGuest;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/sf/test")
@Api("顺丰接口测试")
@Slf4j
public class SfExpressController {
    @Resource
    private SfexpressService sfexpressService;
    @Resource
    private ICusRequestHistoryService cusRequestHistoryService;
    @Resource
    private ICusReceiveHistoryService cusReceiveHistoryService;

    @Value("${cus.testUrl:https://sfapi.sit.sf-express.com:45273/std/service}")
    private String testUrl;

    @Value("${cus.checkWord:93558LGRXSUP}")
    private String checkWord;

    @ApiOperation("发起")
    @GetMapping("/test")
    @RequiresGuest
    public void sendRequest() {
        SfexpressRequestDto sfexpressRequestDto = new SfexpressRequestDto();
        SfMsgDataDto sfMsgDataDto = new SfMsgDataDto();
        List<ExtralnfoListDTO> extralnfoListDTOS = sfMsgDataDto.buildExtra("merchant", "B2025080022-0012", "Pro25074123", "123", "123", "手机", "merchant");
        sfMsgDataDto.setExtralnfoListDTOList(extralnfoListDTOS);
        sfMsgDataDto.setWaybillNo("SF1030419504102");
        sfexpressRequestDto.setPartnerID("Shanghua");
        String requestId = UUID.fastUUID().toString(true);
        sfexpressRequestDto.setRequestId(requestId);
        sfexpressRequestDto.setServiceCode("EXP_RECE_SUBSIDY_ORDER");
        long timestamp = System.currentTimeMillis();
        sfexpressRequestDto.setTimestamp("" + timestamp);
        String jsonString = JSON.toJSONString(sfMsgDataDto);
        sfexpressRequestDto.setMsgData(jsonString);
        sfexpressRequestDto.setMsgDigest("" + this.getCheckWord(jsonString, "" + timestamp, "93558LGRXSUP"));

        SfExpressResponse jsonObject = sfexpressService.sendRequest(testUrl, sfexpressRequestDto);
        SfExpressFinalResponse sfExpressFinalResponse = JSON.parseObject(jsonObject.getApiResultData(), SfExpressFinalResponse.class);
        CusRequestHistory cusRequestHistory = new CusRequestHistory();
        cusRequestHistory.setRequestStr(JSON.toJSONString(sfexpressRequestDto));
        cusRequestHistory.setResponseStr1(JSON.toJSONString(jsonObject));
        cusRequestHistory.setSuccessFlag1(jsonObject.isSuccess() ? "Y" : "N");
        cusRequestHistory.setResponseStr2(JSON.toJSONString(sfExpressFinalResponse));
        cusRequestHistory.setSuccessFlag2(sfExpressFinalResponse.isSuccess() ? "Y" : "N");
        cusRequestHistory.setTotalFlag(cusRequestHistory.getSuccessFlag1().equals("Y") && cusRequestHistory.getSuccessFlag2().equals("Y") ? "Y" : "N");
        cusRequestHistory.setRequestId(requestId);
        cusRequestHistory.setCreateBy("admin");
        cusRequestHistory.setUpdateBy("admin");
        cusRequestHistory.setRemark("");
        cusRequestHistoryService.insertCusRequestHistory(cusRequestHistory);
    }

    @ApiOperation("接收国补信息推送")
    @PostMapping("/receive")
    @RequiresGuest
    public ResponseToSfexpressDto receiveRequest(String requestID, String partnerID, String serviceCode, Long timestamp, String msgDigest, String msgData) {
        log.info("requestID: {}, partnerID: {}, serviceCode: {}, timestamp: {}, msgDigest: {}, msgData: {}", requestID, partnerID, serviceCode, timestamp, msgDigest, msgData);
        this.dealReceive(requestID, partnerID, serviceCode, timestamp, msgDigest, msgData);
        return ResponseToSfexpressDto.success();
    }

    public void dealReceive(String requestID, String partnerID, String serviceCode, Long timestamp, String msgDigest, String msgData) {
        RequestBySfexpressDto javaObject = JSON.toJavaObject(JSON.parseObject(msgData), RequestBySfexpressDto.class);
        CusReceiveHistory build = CusReceiveHistory.builder()
                .requestId(requestID).partnerId(partnerID).serviceCode(serviceCode).timestamp(new Date(timestamp)).msgData(msgData).msgDigest(msgDigest)
                .sn(javaObject.getSn())
                .wayBillNo(javaObject.getWaybillNo())
                .imei(javaObject.getImei())
                .wpServiceCode(javaObject.getWpServiceCode())
                .build();
        cusReceiveHistoryService.insertCusReceiveHistory(build);
        cusReceiveHistoryService.asyncDealPhoto(build.getId());
        log.info("");
    }


    //工具方法，按顺序拼接：msgData（业务报文）+ timestamp+checkWord；经过UTF-8后进行MD5，最后在转换为Base64字符串;
    @SneakyThrows
    private String getCheckWord(String msgData, String timestamp, String checkWord) {
        String toVerify = msgData + timestamp + checkWord;
        toVerify = URLEncoder.encode(toVerify, "UTF-8");
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        md5.update(toVerify.getBytes("UTF-8"));
        byte[] digest = md5.digest();
        String s1 = new String(Base64Encoder.encode(digest));
        return s1;
    }

    @PostMapping("/encode")
    @ApiOperation("测试加密")
    public String getEncode(String str) {
        AES256CipherExternal aes256 = new AES256CipherExternal(checkWord);
        String s = null;
        try {
            s = aes256.AES_Encode(str);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return s;
    }
}

package com.ruoyi.system.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.CusRequestHistory;
import com.ruoyi.system.service.ICusRequestHistoryService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 接口请求记录Controller
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
@Controller
@RequestMapping("/system/history")
public class CusRequestHistoryController extends BaseController
{
    private String prefix = "system/history";

    @Autowired
    private ICusRequestHistoryService cusRequestHistoryService;

    @RequiresPermissions("system:history:view")
    @GetMapping()
    public String history()
    {
        return prefix + "/history";
    }

    /**
     * 查询接口请求记录列表
     */
    @RequiresPermissions("system:history:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(CusRequestHistory cusRequestHistory)
    {
        startPage();
        List<CusRequestHistory> list = cusRequestHistoryService.selectCusRequestHistoryList(cusRequestHistory);
        return getDataTable(list);
    }

    /**
     * 导出接口请求记录列表
     */
    @RequiresPermissions("system:history:export")
    @Log(title = "接口请求记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(CusRequestHistory cusRequestHistory)
    {
        List<CusRequestHistory> list = cusRequestHistoryService.selectCusRequestHistoryList(cusRequestHistory);
        ExcelUtil<CusRequestHistory> util = new ExcelUtil<CusRequestHistory>(CusRequestHistory.class);
        return util.exportExcel(list, "接口请求记录数据");
    }

    /**
     * 新增接口请求记录
     */
    @RequiresPermissions("system:history:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存接口请求记录
     */
    @RequiresPermissions("system:history:add")
    @Log(title = "接口请求记录", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(CusRequestHistory cusRequestHistory)
    {
        return toAjax(cusRequestHistoryService.insertCusRequestHistory(cusRequestHistory));
    }

    /**
     * 修改接口请求记录
     */
    @RequiresPermissions("system:history:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        CusRequestHistory cusRequestHistory = cusRequestHistoryService.selectCusRequestHistoryById(id);
        mmap.put("cusRequestHistory", cusRequestHistory);
        return prefix + "/edit";
    }

    @RequiresPermissions("system:history:edit")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id") String id, ModelMap mmap)
    {
        CusRequestHistory search = new CusRequestHistory();
        search.setRequestId(id);
        CusRequestHistory cusRequestHistory = cusRequestHistoryService.selectCusRequestHistoryList(search).get(0);
        mmap.put("cusRequestHistory", cusRequestHistory);
        return prefix + "/detail";
    }

    /**
     * 修改保存接口请求记录
     */
    @RequiresPermissions("system:history:edit")
    @Log(title = "接口请求记录", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(CusRequestHistory cusRequestHistory)
    {
        return toAjax(cusRequestHistoryService.updateCusRequestHistory(cusRequestHistory));
    }

    /**
     * 删除接口请求记录
     */
    @RequiresPermissions("system:history:remove")
    @Log(title = "接口请求记录", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(cusRequestHistoryService.deleteCusRequestHistoryByIds(ids));
    }
}

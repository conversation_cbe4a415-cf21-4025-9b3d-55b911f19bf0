package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.CusOrderInfo;
import com.ruoyi.system.service.ICusOrderInfoService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 订单信息Controller
 *
 * <AUTHOR>
 * @date 2025-09-03
 */
@Controller
@RequestMapping("/system/orderInfo")
public class CusOrderInfoController extends BaseController {
    private String prefix = "system/orderInfo";

    @Autowired
    private ICusOrderInfoService cusOrderInfoService;

    @RequiresPermissions("system:orderInfo:view")
    @GetMapping()
    public String orderInfo() {
        return prefix + "/orderInfo";
    }

    /**
     * 查询订单信息列表
     */
    @RequiresPermissions("system:orderInfo:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(CusOrderInfo cusOrderInfo) {
        startPage();
        List<CusOrderInfo> list = cusOrderInfoService.selectCusOrderInfoList(cusOrderInfo);
        return getDataTable(list);
    }

    /**
     * 导出订单信息列表
     */
    @RequiresPermissions("system:orderInfo:export")
    @Log(title = "订单信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(CusOrderInfo cusOrderInfo) {
        List<CusOrderInfo> list = cusOrderInfoService.selectCusOrderInfoList(cusOrderInfo);
        ExcelUtil<CusOrderInfo> util = new ExcelUtil<CusOrderInfo>(CusOrderInfo.class);
        return util.exportExcel(list, "订单信息数据");
    }

    /**
     * 新增订单信息
     */
    @RequiresPermissions("system:orderInfo:add")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存订单信息
     */
    @RequiresPermissions("system:orderInfo:add")
    @Log(title = "订单信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(CusOrderInfo cusOrderInfo) {
        return toAjax(cusOrderInfoService.insertCusOrderInfo(cusOrderInfo));
    }

    /**
     * 修改订单信息
     */
    @RequiresPermissions("system:orderInfo:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        CusOrderInfo cusOrderInfo = cusOrderInfoService.selectCusOrderInfoById(id);
        mmap.put("cusOrderInfo", cusOrderInfo);
        return prefix + "/edit";
    }

    /**
     * 修改保存订单信息
     */
    @RequiresPermissions("system:orderInfo:edit")
    @Log(title = "订单信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(CusOrderInfo cusOrderInfo) {
        return toAjax(cusOrderInfoService.updateCusOrderInfo(cusOrderInfo));
    }

    /**
     * 删除订单信息
     */
    @RequiresPermissions("system:orderInfo:remove")
    @Log(title = "订单信息", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(cusOrderInfoService.deleteCusOrderInfoByIds(ids));
    }

    @Log(title = "订单信息", businessType = BusinessType.IMPORT)
    @RequiresPermissions("system:orderInfo:import")
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<CusOrderInfo> util = new ExcelUtil<CusOrderInfo>(CusOrderInfo.class);
        List<CusOrderInfo> userList = util.importExcel(file.getInputStream());
        cusOrderInfoService.importUser(userList);
        return AjaxResult.success();
    }

    /**
     * 下载模板
     */
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate() {
        ExcelUtil<CusOrderInfo> util = new ExcelUtil<CusOrderInfo>(CusOrderInfo.class);
        return util.importTemplateExcel("订单数据");
    }

    /**
     * 刷新参数缓存
     */
    //@RequiresPermissions("system:config:remove")
    @Log(title = "订单信息", businessType = BusinessType.CLEAN)
    @GetMapping("/touchCall")
    @ResponseBody
    public AjaxResult touchCall() {
        cusOrderInfoService.touchCall();
        return success();
    }



    //@RequiresPermissions("system:orderInfo:remove")
    @Log(title = "订单信息", businessType = BusinessType.DELETE)
    @PostMapping("/touchAll")
    @ResponseBody
    public AjaxResult touchAll(String ids) {
        return toAjax(cusOrderInfoService.touchCallByIds(ids));
    }
}

package com.ruoyi.system.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
@AllArgsConstructor
@Builder
public class ResponseToSfexpressDto {
    @JSONField(name = "success")
    private Boolean success;
    @JSONField(name = "retryFlag")
    private Boolean retryFlag;
    @JSONField(name = "errorCode")
    private String errorCode;
    @JSONField(name = "errorMsg")
    private String errorMsg;

    //成功返回体
    public static ResponseToSfexpressDto success() {
        return ResponseToSfexpressDto.builder().success(true).retryFlag(false).build();
    }

    //失败返回体
    public static ResponseToSfexpressDto fail() {
        return ResponseToSfexpressDto.builder()
                .success(false).retryFlag(true)
                .errorCode("500").errorMsg("系统错误")
                .build();
    }
}

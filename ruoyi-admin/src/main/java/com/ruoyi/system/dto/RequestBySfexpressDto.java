package com.ruoyi.system.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class RequestBySfexpressDto {
    @J<PERSON>NField(name = "photoList")
    private List<PhotoListDTO> photoList;
    @JSONField(name = "wpServiceCode")
    private String wpServiceCode;
    @JSONField(name = "completeTime")
    private String completeTime;
    @JSONField(name = "imei")
    private String imei;
    @J<PERSON><PERSON>ield(name = "sn")
    private String sn;
    @J<PERSON>NField(name = "waybillNo")
    private String waybillNo;
    @JSONField(name = "scanResult")
    private List<ScanResultDTO> scanResult;

    @NoArgsConstructor
    @Data
    public static class PhotoListDTO {
        @JSONField(name = "photoUrl")
        private String photoUrl;
        @JSONField(name = "photoType")
        private String photoType;
        @J<PERSON><PERSON>ield(name = "photoTime")
        private String photoTime;
    }

    @NoArgsConstructor
    @Data
    public static class ScanResultDTO {
        @J<PERSON><PERSON><PERSON>(name = "code")
        private String code;
        @J<PERSON><PERSON>ield(name = "details")
        private List<DetailsDTO> details;

        @NoArgsConstructor
        @Data
        public static class DetailsDTO {
            @JSONField(name = "verifyCode")
            private String verifyCode;
            @JSONField(name = "createTime")
            private String createTime;
            @JSONField(name = "status")
            private String status;
        }
    }
}

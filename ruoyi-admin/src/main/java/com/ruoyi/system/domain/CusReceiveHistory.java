package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 接口响应记录对象 cus_receive_history
 * 
 * <AUTHOR>
 * @date 2025-09-06
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class CusReceiveHistory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 请求id */
    @Excel(name = "请求id")
    private String requestId;

    /** 合作伙伴编码 */
    @Excel(name = "合作伙伴编码")
    private String partnerId;

    /** 合作伙伴编码 */
    @Excel(name = "合作伙伴编码")
    private String serviceCode;

    /** 时间戳 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "时间戳", width = 30, dateFormat = "yyyy-MM-dd")
    private Date timestamp;

    /** 数字签名 */
    @Excel(name = "数字签名")
    private String msgDigest;

    /** 业务数据报文 */
    @Excel(name = "业务数据报文")
    private String msgData;

    /** 是否处理 */
    @Excel(name = "是否处理")
    private String finalResult;

    /** 客户运单号 */
    @Excel(name = "客户运单号")
    private String wayBillNo;

    /** 微派任务编码 */
    @Excel(name = "微派任务编码")
    private String wpServiceCode;

    /** imei */
    @Excel(name = "imei")
    private String imei;

    /** sn */
    @Excel(name = "sn")
    private String sn;

    /** 图片列表 */
    @Excel(name = "图片列表")
    private String photoList;


    private String localPhotoList;

    public String getLocalPhotoList() {
        return localPhotoList;
    }

    public void setLocalPhotoList(String localPhotoList) {
        this.localPhotoList = localPhotoList;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setRequestId(String requestId) 
    {
        this.requestId = requestId;
    }

    public String getRequestId() 
    {
        return requestId;
    }

    public void setPartnerId(String partnerId) 
    {
        this.partnerId = partnerId;
    }

    public String getPartnerId() 
    {
        return partnerId;
    }

    public void setServiceCode(String serviceCode) 
    {
        this.serviceCode = serviceCode;
    }

    public String getServiceCode() 
    {
        return serviceCode;
    }

    public void setTimestamp(Date timestamp) 
    {
        this.timestamp = timestamp;
    }

    public Date getTimestamp() 
    {
        return timestamp;
    }

    public void setMsgDigest(String msgDigest) 
    {
        this.msgDigest = msgDigest;
    }

    public String getMsgDigest() 
    {
        return msgDigest;
    }

    public void setMsgData(String msgData) 
    {
        this.msgData = msgData;
    }

    public String getMsgData() 
    {
        return msgData;
    }

    public void setFinalResult(String finalResult) 
    {
        this.finalResult = finalResult;
    }

    public String getFinalResult() 
    {
        return finalResult;
    }

    public void setWayBillNo(String wayBillNo) 
    {
        this.wayBillNo = wayBillNo;
    }

    public String getWayBillNo() 
    {
        return wayBillNo;
    }

    public void setWpServiceCode(String wpServiceCode) 
    {
        this.wpServiceCode = wpServiceCode;
    }

    public String getWpServiceCode() 
    {
        return wpServiceCode;
    }

    public void setImei(String imei) 
    {
        this.imei = imei;
    }

    public String getImei() 
    {
        return imei;
    }

    public void setSn(String sn) 
    {
        this.sn = sn;
    }

    public String getSn() 
    {
        return sn;
    }

    public void setPhotoList(String photoList) 
    {
        this.photoList = photoList;
    }

    public String getPhotoList() 
    {
        return photoList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("requestId", getRequestId())
            .append("partnerId", getPartnerId())
            .append("serviceCode", getServiceCode())
            .append("timestamp", getTimestamp())
            .append("msgDigest", getMsgDigest())
            .append("msgData", getMsgData())
            .append("finalResult", getFinalResult())
            .append("wayBillNo", getWayBillNo())
            .append("wpServiceCode", getWpServiceCode())
            .append("imei", getImei())
            .append("sn", getSn())
            .append("photoList", getPhotoList())
            .toString();
    }
}

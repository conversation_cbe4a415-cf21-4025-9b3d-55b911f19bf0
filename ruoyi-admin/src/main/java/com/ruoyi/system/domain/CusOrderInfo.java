package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 订单信息对象 cus_order_info
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
public class CusOrderInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 客户运单号 */
    @Excel(name = "客户运单号")
    private String waybillNo;

    /** 国补标识 */
    @Excel(name = "国补标识")
    private String subsidySource;

    /** 微派商户编码 */
    @Excel(name = "微派商户编码")
    private String wpMerchantCode;

    /** 微派任务编码 */
    @Excel(name = "微派任务编码")
    private String wpServiceCode;

    /** 托寄物名称 */
    @Excel(name = "托寄物名称")
    private String cargoName;

    /** sn编码 */
    @Excel(name = "sn编码")
    private String snCode;

    /** imei编码 */
    @Excel(name = "imei编码")
    private String imelCode;

    /** 手机号码后四位 */
    @Excel(name = "手机号码后四位")
    private String checkPhoneNo;

    /** 月结卡号 */
    @Excel(name = "月结卡号")
    private String checkMonthCard;

    private String finalResult;

    private String logId;

    private String localPhotoList;

    public String getLocalPhotoList() {
        return localPhotoList;
    }

    public void setLocalPhotoList(String localPhotoList) {
        this.localPhotoList = localPhotoList;
    }

    public String getFinalResult() {
        return finalResult;
    }

    public void setFinalResult(String finalResult) {
        this.finalResult = finalResult;
    }

    public String getLogId() {
        return logId;
    }

    public void setLogId(String logId) {
        this.logId = logId;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setWaybillNo(String waybillNo)
    {
        this.waybillNo = waybillNo;
    }

    public String getWaybillNo()
    {
        return waybillNo;
    }

    public void setSubsidySource(String subsidySource) 
    {
        this.subsidySource = subsidySource;
    }

    public String getSubsidySource() 
    {
        return subsidySource;
    }

    public void setWpMerchantCode(String wpMerchantCode) 
    {
        this.wpMerchantCode = wpMerchantCode;
    }

    public String getWpMerchantCode() 
    {
        return wpMerchantCode;
    }

    public void setWpServiceCode(String wpServiceCode) 
    {
        this.wpServiceCode = wpServiceCode;
    }

    public String getWpServiceCode() 
    {
        return wpServiceCode;
    }

    public void setCargoName(String cargoName) 
    {
        this.cargoName = cargoName;
    }

    public String getCargoName() 
    {
        return cargoName;
    }

    public void setSnCode(String snCode) 
    {
        this.snCode = snCode;
    }

    public String getSnCode() 
    {
        return snCode;
    }

    public void setImelCode(String imelCode) 
    {
        this.imelCode = imelCode;
    }

    public String getImelCode() 
    {
        return imelCode;
    }

    public void setCheckPhoneNo(String checkPhoneNo)
    {
        this.checkPhoneNo = checkPhoneNo;
    }

    public String getCheckPhoneNo()
    {
        return checkPhoneNo;
    }

    public void setCheckMonthCard(String checkMonthCard) 
    {
        this.checkMonthCard = checkMonthCard;
    }

    public String getCheckMonthCard() 
    {
        return checkMonthCard;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("waybillNo", getWaybillNo())
            .append("subsidySource", getSubsidySource())
            .append("wpMerchantCode", getWpMerchantCode())
            .append("wpServiceCode", getWpServiceCode())
            .append("cargoName", getCargoName())
            .append("snCode", getSnCode())
            .append("imelCode", getImelCode())
            .append("checkPhoneNo", getCheckPhoneNo())
            .append("checkMonthCard", getCheckMonthCard())
            .toString();
    }
}

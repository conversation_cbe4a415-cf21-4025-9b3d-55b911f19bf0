package com.ruoyi.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 接口请求记录对象 cus_request_history
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
@Data
public class CusRequestHistory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "请求时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**  */
    private Long id;

    /** 请求体 */
    @Excel(name = "请求体")
    private String requestStr;

    /** 一级响应体 */
    @Excel(name = "一级响应体")
    private String responseStr1;

    /** 二级响应体 */
    @Excel(name = "二级响应体")
    private String responseStr2;

    /** 一级是否成功 */
    @Excel(name = "一级是否成功")
    private String successFlag1;

    /** 二级是否成功 */
    @Excel(name = "二级是否成功")
    private String successFlag2;

    /** 总体是否成功 */
    @Excel(name = "总体是否成功")
    private String totalFlag;

    /** 请求id */
    @Excel(name = "请求id")
    private String requestId;



    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setRequestStr(String requestStr) 
    {
        this.requestStr = requestStr;
    }

    public String getRequestStr() 
    {
        return requestStr;
    }

    public void setResponseStr1(String responseStr1) 
    {
        this.responseStr1 = responseStr1;
    }

    public String getResponseStr1() 
    {
        return responseStr1;
    }

    public void setResponseStr2(String responseStr2) 
    {
        this.responseStr2 = responseStr2;
    }

    public String getResponseStr2() 
    {
        return responseStr2;
    }

    public void setSuccessFlag1(String successFlag1) 
    {
        this.successFlag1 = successFlag1;
    }

    public String getSuccessFlag1() 
    {
        return successFlag1;
    }

    public void setSuccessFlag2(String successFlag2) 
    {
        this.successFlag2 = successFlag2;
    }

    public String getSuccessFlag2() 
    {
        return successFlag2;
    }

    public void setTotalFlag(String totalFlag) 
    {
        this.totalFlag = totalFlag;
    }

    public String getTotalFlag() 
    {
        return totalFlag;
    }

    public void setRequestId(String requestId) 
    {
        this.requestId = requestId;
    }

    public String getRequestId() 
    {
        return requestId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("requestStr", getRequestStr())
            .append("responseStr1", getResponseStr1())
            .append("responseStr2", getResponseStr2())
            .append("successFlag1", getSuccessFlag1())
            .append("successFlag2", getSuccessFlag2())
            .append("totalFlag", getTotalFlag())
            .append("requestId", getRequestId())
            .toString();
    }
}

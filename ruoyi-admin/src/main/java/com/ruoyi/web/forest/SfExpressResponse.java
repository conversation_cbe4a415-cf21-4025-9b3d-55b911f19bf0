package com.ruoyi.web.forest;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class SfExpressResponse {
    @J<PERSON>NField(name = "apiResponseID")
    private String apiResponseID;
    @JSONField(name = "apiErrorMsg")
    private String apiErrorMsg;
    @JSONField(name = "apiResultCode")
    private String apiResultCode;
    @JSONField(name = "apiResultData")
    private String apiResultData;

    //判断请求是否成功，以apiResultCode字段为准
    public boolean isSuccess() {
        return "A1000".equals(apiResultCode);
    }
}

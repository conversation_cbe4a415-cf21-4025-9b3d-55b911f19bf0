package com.ruoyi.web.forest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.ruoyi.common.utils.StringUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.compress.utils.Lists;

import java.util.List;

@NoArgsConstructor
@Data
public class SfMsgDataDto {
    @JSONField(name = "waybillNo")
    private String waybillNo;
    @JSONField(name = "checkPhoneNo")
    private String checkPhoneNo;
    @JSONField(name = "checkMonthCard")
    private String checkMonthCard;
    @J<PERSON>NField(name = "extraInfoList")
    private List<ExtralnfoListDTO> extralnfoListDTOList;


    public List<ExtralnfoListDTO> buildExtra(String subsidySource, String wpMerchantCode,
                                             String wpServiceCode, String snCode, String imeiCode,
                                             String cargoName, String infoCollectSource) {
        List<ExtralnfoListDTO> extralnfoList = Lists.newArrayList();
        if (StringUtils.isNotBlank(subsidySource)) {
            extralnfoList.add(ExtralnfoListDTO.builder().attrName("subsidySource").attrVal(subsidySource).build());
        }
        if (StringUtils.isNotBlank(wpMerchantCode)) {
            extralnfoList.add(ExtralnfoListDTO.builder().attrName("wpMerchantCode").attrVal(wpMerchantCode).build());
        }
        if (StringUtils.isNotBlank(wpServiceCode)) {
            extralnfoList.add(ExtralnfoListDTO.builder().attrName("wpServiceCode").attrVal(wpServiceCode).build());
        }
        if (StringUtils.isNotBlank(snCode) || StringUtils.isNotBlank(imeiCode)) {
            SfSnCodeDto build = SfSnCodeDto.builder().build();
            if (StringUtils.isNotBlank(snCode)){
                build.setSn(snCode);
            }
            if (StringUtils.isNotBlank(imeiCode)){
                build.setImei(imeiCode);
            }
            extralnfoList.add(ExtralnfoListDTO.builder().attrName("wpExtJson").attrVal(JSON.toJSONString(build)).build());
        }
        if (StringUtils.isNotBlank(cargoName)) {
            extralnfoList.add(ExtralnfoListDTO.builder().attrName("cargoName").attrVal(cargoName).build());
        }
//        if (StringUtils.isNotBlank(infoCollectSource)) {
//            extralnfoList.add(ExtralnfoListDTO.builder().attrName("infoCollectSource").build());
//        }
        return extralnfoList;
    }
}

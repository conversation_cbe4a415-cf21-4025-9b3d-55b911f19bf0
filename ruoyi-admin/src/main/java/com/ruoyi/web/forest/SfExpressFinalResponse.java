package com.ruoyi.web.forest;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class SfExpressFinalResponse {
    @JSONField(name = "success")
    private Boolean success;
    @JSONField(name = "errorCode")
    private String errorCode;
    @JSONField(name = "errorMsg")
    private String errorMsg;
    @JSONField(name = "msgData")
    private JSONObject msgData;

    //判断请求是否成功，以errorCode字段为准
    public boolean isSuccess() {
        return success;
    }
}

package com.ruoyi.web.forest;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
public class SfexpressRequestDto {
//    @JSONField(name = "ak")
//    private String ak;
//    @JSONField(name = "clientCode")
//    private String clientCode;

    @JSONField(name = "partnerID")
    private String partnerID;

    @JSONField(name = "requestID")
    private String requestId;

    @JSONField(name = "serviceCode")
    private String serviceCode;

    @JSONField(name = "timestamp")
    private String timestamp;

    @JSONField(name = "msgDigest")
    private String msgDigest;

    @JSONField(name = "msgData")
    private String msgData;
}
